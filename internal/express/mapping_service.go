package express

import (
	"context"
	"fmt"
	"strings"

	"go.uber.org/zap"
)

// ExpressMappingService 快递公司映射服务接口
type ExpressMappingService interface {
	// GetProviderCompanyCode 获取供应商特定的快递公司代码
	GetProviderCompanyCode(ctx context.Context, companyCode, providerCode string) (string, error)

	// GetStandardCompanyCode 根据供应商代码获取标准快递公司代码
	GetStandardCompanyCode(ctx context.Context, providerCompanyCode, providerCode string) (string, error)

	// GetCompanyMapping 获取快递公司映射信息
	GetCompanyMapping(ctx context.Context, companyCode, providerCode string) (*ExpressMappingCache, error)

	// GetSupportedCompanies 获取供应商支持的快递公司列表
	GetSupportedCompanies(ctx context.Context, providerCode string) ([]*ExpressMappingCache, error)

	// 🔥 新增：无缓存查询供应商支持的快递公司列表（直接查询数据库）
	GetProviderSupportedCompaniesNoCache(ctx context.Context, providerCode string) ([]*ExpressMappingCache, error)

	// GetPreferredProvider 获取快递公司的首选供应商
	GetPreferredProvider(ctx context.Context, companyCode string) (*ExpressMappingCache, error)

	// GetProductType 获取产品类型映射
	GetProductType(ctx context.Context, companyCode, providerCode, providerCompanyCode string) (string, error)

	// GetPayMethod 获取支付方式映射
	GetPayMethod(ctx context.Context, companyCode, providerCode, payMethodCode string) (string, error)

	// GetVolumeWeightRatio 获取体积重量系数
	GetVolumeWeightRatio(ctx context.Context, companyCode string) (int, error)

	// RefreshCache 刷新缓存
	RefreshCache(ctx context.Context) error

	// 🚀 新增：强制刷新特定供应商的缓存，确保映射变更立即生效
	RefreshProviderCache(ctx context.Context, providerCode string) error

	// 🚀 新增：批量获取所有供应商的映射关系，用于性能优化
	GetAllProviderMappings(ctx context.Context, providers []string) (map[string]map[string]bool, error)
}

// DefaultExpressMappingService 默认快递公司映射服务实现
type DefaultExpressMappingService struct {
	cacheService *ExpressMappingCacheService
	repository   ExpressCompanyRepository
	logger       *zap.Logger
}

// NewExpressMappingService 创建快递公司映射服务
func NewExpressMappingService(repository ExpressCompanyRepository, logger *zap.Logger) ExpressMappingService {
	cacheService := NewExpressMappingCacheService(repository, logger)

	return &DefaultExpressMappingService{
		cacheService: cacheService,
		repository:   repository,
		logger:       logger,
	}
}

// GetProviderCompanyCode 获取供应商特定的快递公司代码
func (s *DefaultExpressMappingService) GetProviderCompanyCode(ctx context.Context, companyCode, providerCode string) (string, error) {
	return s.cacheService.GetProviderCompanyCode(ctx, companyCode, providerCode)
}

// GetStandardCompanyCode 根据供应商代码获取标准快递公司代码
func (s *DefaultExpressMappingService) GetStandardCompanyCode(ctx context.Context, providerCompanyCode, providerCode string) (string, error) {
	// 获取供应商支持的所有快递公司
	companies, err := s.cacheService.GetSupportedCompanies(ctx, providerCode)
	if err != nil {
		return "", err
	}

	normalized := strings.ToUpper(strings.TrimSpace(providerCompanyCode))
	for _, company := range companies {
		if strings.ToUpper(strings.TrimSpace(company.ProviderCompanyCode)) == normalized {
			return company.CompanyCode, nil
		}
	}

	// ---------- 回退逻辑 ----------
	// 如果在数据库/缓存中未找到，尝试使用内置静态映射表
	fallback := map[string]string{
		"YD":      "YUNDA", // 韵达
		"YUNDA":   "YUNDA",
		"YTO":     "YTO", // 圆通
		"ZTO":     "ZTO", // 中通
		"STO":     "STO", // 申通
		"JT":      "JT",  // 极兔
		"SF":      "SF",  // 顺丰
		"EMS":     "EMS",
		"DBL":     "DBL",     // 德邦快递
		"DOP":     "DBL",     // 德邦快递（易达供应商代码）
		"JD":      "JD",      // 京东物流
		"CAINIAO": "CAINIAO", // 🔧 新增：菜鸟裹裹标准快递
		"NORMAL":  "CAINIAO", // 🔧 新增：NORMAL映射为CAINIAO
	}

	if standard, ok := fallback[providerCompanyCode]; ok {
		// 记录日志，提示需要在数据库中补充正式映射
		if s.logger != nil {
			s.logger.Debug("使用静态回退快递映射", zap.String("provider_code", providerCompanyCode), zap.String("standard_code", standard))
		}
		return standard, nil
	}

	return "", fmt.Errorf("未找到供应商代码 %s 对应的标准快递公司代码", providerCompanyCode)
}

// GetCompanyMapping 获取快递公司映射信息
func (s *DefaultExpressMappingService) GetCompanyMapping(ctx context.Context, companyCode, providerCode string) (*ExpressMappingCache, error) {
	return s.cacheService.GetCompanyMapping(ctx, companyCode, providerCode)
}

// GetSupportedCompanies 获取供应商支持的快递公司列表
func (s *DefaultExpressMappingService) GetSupportedCompanies(ctx context.Context, providerCode string) ([]*ExpressMappingCache, error) {
	return s.cacheService.GetSupportedCompanies(ctx, providerCode)
}

// 🔥 新增：无缓存查询供应商支持的快递公司列表（直接查询数据库）
func (s *DefaultExpressMappingService) GetProviderSupportedCompaniesNoCache(ctx context.Context, providerCode string) ([]*ExpressMappingCache, error) {
	return s.cacheService.GetProviderSupportedCompaniesNoCache(ctx, providerCode)
}

// GetPreferredProvider 获取快递公司的首选供应商
func (s *DefaultExpressMappingService) GetPreferredProvider(ctx context.Context, companyCode string) (*ExpressMappingCache, error) {
	return s.cacheService.GetPreferredProvider(ctx, companyCode)
}

// GetProductType 获取产品类型映射
func (s *DefaultExpressMappingService) GetProductType(ctx context.Context, companyCode, providerCode, providerCompanyCode string) (string, error) {
	mapping, err := s.GetCompanyMapping(ctx, companyCode, providerCode)
	if err != nil {
		return "", err
	}

	if mapping.ProductTypeMappings == nil {
		return "标准快递", nil // 默认产品类型
	}

	if productType, exists := mapping.ProductTypeMappings[providerCompanyCode]; exists {
		return productType, nil
	}

	return "标准快递", nil // 默认产品类型
}

// GetPayMethod 获取支付方式映射
func (s *DefaultExpressMappingService) GetPayMethod(ctx context.Context, companyCode, providerCode, payMethodCode string) (string, error) {
	mapping, err := s.GetCompanyMapping(ctx, companyCode, providerCode)
	if err != nil {
		return "", err
	}

	if mapping.PayMethodMappings == nil {
		return payMethodCode, nil // 返回原始代码
	}

	if payMethod, exists := mapping.PayMethodMappings[payMethodCode]; exists {
		return payMethod, nil
	}

	return payMethodCode, nil // 返回原始代码
}

// GetVolumeWeightRatio 获取体积重量系数
// 严格从数据库获取，无默认值逻辑
func (s *DefaultExpressMappingService) GetVolumeWeightRatio(ctx context.Context, companyCode string) (int, error) {
	// 直接从快递公司表获取抛比配置，不依赖供应商映射
	if s.repository != nil {
		company, err := s.repository.GetCompanyByCode(companyCode)
		if err != nil {
			s.logger.Error("获取快递公司配置失败",
				zap.String("company_code", companyCode),
				zap.Error(err))
			return 0, fmt.Errorf("快递公司 %s 不存在或配置错误: %w。请在数据库 express_companies 表中配置正确的快递公司信息", companyCode, err)
		}

		if !company.IsActive {
			return 0, fmt.Errorf("快递公司 %s 已禁用。请在数据库中启用该快递公司或选择其他快递公司", companyCode)
		}

		if company.VolumeWeightRatio <= 0 {
			return 0, fmt.Errorf("快递公司 %s 抛比配置无效: %d。请在数据库 express_companies 表中配置正确的 volume_weight_ratio 值（建议值：顺丰5000，其他8000）", companyCode, company.VolumeWeightRatio)
		}

		s.logger.Debug("从数据库获取抛比配置",
			zap.String("company_code", companyCode),
			zap.String("company_name", company.Name),
			zap.Int("volume_ratio", company.VolumeWeightRatio))

		return company.VolumeWeightRatio, nil
	}

	return 0, fmt.Errorf("快递公司仓库未初始化，无法获取抛比配置")
}

// RefreshCache 刷新缓存
func (s *DefaultExpressMappingService) RefreshCache(ctx context.Context) error {
	return s.cacheService.RefreshCache(ctx)
}

// 🚀 新增：强制刷新特定供应商的缓存，确保映射变更立即生效
func (s *DefaultExpressMappingService) RefreshProviderCache(ctx context.Context, providerCode string) error {
	return s.cacheService.RefreshProviderCache(ctx, providerCode)
}

// 🚀 新增：批量获取所有供应商的映射关系，用于性能优化
func (s *DefaultExpressMappingService) GetAllProviderMappings(ctx context.Context, providers []string) (map[string]map[string]bool, error) {
	allMappings := make(map[string]map[string]bool)

	for _, providerCode := range providers {
		companies, err := s.GetSupportedCompanies(ctx, providerCode)
		if err != nil {
			return nil, fmt.Errorf("获取供应商 %s 支持的快递公司失败: %w", providerCode, err)
		}

		providerMapping := make(map[string]bool)
		for _, company := range companies {
			providerMapping[company.CompanyCode] = true
		}
		allMappings[providerCode] = providerMapping
	}

	return allMappings, nil
}

// ExpressMappingHelper 快递公司映射辅助函数
type ExpressMappingHelper struct {
	mappingService ExpressMappingService
}

// NewExpressMappingHelper 创建快递公司映射辅助工具
func NewExpressMappingHelper(mappingService ExpressMappingService) *ExpressMappingHelper {
	return &ExpressMappingHelper{
		mappingService: mappingService,
	}
}

// ConvertExpressCode 转换快递公司代码（兼容旧的配置文件方式）
func (h *ExpressMappingHelper) ConvertExpressCode(ctx context.Context, standardCode, providerCode string) string {
	providerCompanyCode, err := h.mappingService.GetProviderCompanyCode(ctx, standardCode, providerCode)
	if err != nil {
		// 如果转换失败，返回原始代码
		return standardCode
	}
	return providerCompanyCode
}

// GetExpressCodeMapping 获取快递公司代码映射（兼容旧的配置文件方式）
func (h *ExpressMappingHelper) GetExpressCodeMapping(ctx context.Context, providerCode string) map[string]string {
	companies, err := h.mappingService.GetSupportedCompanies(ctx, providerCode)
	if err != nil {
		return make(map[string]string)
	}

	mapping := make(map[string]string)
	for _, company := range companies {
		mapping[company.CompanyCode] = company.ProviderCompanyCode
	}

	return mapping
}

// IsExpressSupported 检查快递公司是否被供应商支持
func (h *ExpressMappingHelper) IsExpressSupported(ctx context.Context, companyCode, providerCode string) bool {
	_, err := h.mappingService.GetProviderCompanyCode(ctx, companyCode, providerCode)
	return err == nil
}

// GetBestProvider 获取快递公司的最佳供应商（首选或优先级最高）
func (h *ExpressMappingHelper) GetBestProvider(ctx context.Context, companyCode string) (string, error) {
	// 首先尝试获取首选供应商
	preferred, err := h.mappingService.GetPreferredProvider(ctx, companyCode)
	if err == nil {
		return preferred.ProviderCode, nil
	}

	// 如果没有首选供应商，按优先级获取
	providers := []string{"yida", "yuntong", "kuaidi100"} // 按优先级排序

	for _, providerCode := range providers {
		if h.IsExpressSupported(ctx, companyCode, providerCode) {
			return providerCode, nil
		}
	}

	return "", fmt.Errorf("未找到支持快递公司 %s 的供应商", companyCode)
}

// ValidateExpressMapping 验证快递公司映射配置
func (h *ExpressMappingHelper) ValidateExpressMapping(ctx context.Context) error {
	// 获取所有供应商
	providers := []string{"kuaidi100", "yida", "yuntong"}

	for _, providerCode := range providers {
		companies, err := h.mappingService.GetSupportedCompanies(ctx, providerCode)
		if err != nil {
			return fmt.Errorf("获取供应商 %s 支持的快递公司失败: %w", providerCode, err)
		}

		if len(companies) == 0 {
			return fmt.Errorf("供应商 %s 没有支持的快递公司", providerCode)
		}

		// 验证每个映射的完整性
		for _, company := range companies {
			if company.CompanyCode == "" || company.ProviderCompanyCode == "" {
				return fmt.Errorf("供应商 %s 的快递公司 %s 映射配置不完整", providerCode, company.CompanyCode)
			}
		}
	}

	return nil
}

// GetMappingStatistics 获取映射统计信息
func (h *ExpressMappingHelper) GetMappingStatistics(ctx context.Context) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	providers := []string{"kuaidi100", "yida", "yuntong"}
	totalMappings := 0
	providerStats := make(map[string]int)

	for _, providerCode := range providers {
		companies, err := h.mappingService.GetSupportedCompanies(ctx, providerCode)
		if err != nil {
			continue
		}

		count := len(companies)
		providerStats[providerCode] = count
		totalMappings += count
	}

	stats["total_mappings"] = totalMappings
	stats["provider_mappings"] = providerStats
	stats["providers"] = providers

	return stats, nil
}
