package express

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"go.uber.org/zap"
)

// SmartCacheManager 智能缓存管理器
// 实现多层缓存、智能预热、增量更新等高性能缓存机制
type SmartCacheManager interface {
	// GetCompanyStatus 获取快递公司状态（带缓存）
	GetCompanyStatus(ctx context.Context, companyCode string) (bool, error)

	// GetProviderStatus 获取供应商状态（带缓存）
	GetProviderStatus(ctx context.Context, providerCode string) (bool, error)

	// GetCompanyMapping 获取映射关系（带缓存）
	GetCompanyMapping(ctx context.Context, companyCode, providerCode string) (*ExpressMappingCache, error)

	// GetEnabledCompanies 获取启用的快递公司列表（带缓存）
	GetEnabledCompanies(ctx context.Context) ([]*ExpressCompany, error)

	// GetEnabledProviders 获取启用的供应商列表（带缓存）
	GetEnabledProviders(ctx context.Context) ([]*ExpressProvider, error)

	// InvalidateCompany 失效快递公司缓存
	InvalidateCompany(ctx context.Context, companyCode string) error

	// InvalidateProvider 失效供应商缓存
	InvalidateProvider(ctx context.Context, providerCode string) error

	// InvalidateMapping 失效映射关系缓存
	InvalidateMapping(ctx context.Context, companyCode, providerCode string) error

	// WarmupCache 预热缓存
	WarmupCache(ctx context.Context) error

	// GetCacheStats 获取缓存统计信息
	GetCacheStats(ctx context.Context) (*CacheStats, error)
}

// CacheStats 缓存统计信息
type CacheStats struct {
	HitCount       int64   `json:"hit_count"`        // 命中次数
	MissCount      int64   `json:"miss_count"`       // 未命中次数
	HitRate        float64 `json:"hit_rate"`         // 命中率
	TotalKeys      int     `json:"total_keys"`       // 总键数
	MemoryUsage    int64   `json:"memory_usage"`     // 内存使用量（字节）
	LastWarmupTime string  `json:"last_warmup_time"` // 最后预热时间
	CacheExpiry    string  `json:"cache_expiry"`     // 缓存过期时间
}

// DefaultSmartCacheManager 默认智能缓存管理器实现
type DefaultSmartCacheManager struct {
	// 依赖服务
	repository          ExpressCompanyRepository
	systemConfigService ConfigServiceInterface
	redisClient         *redis.Client
	logger              *zap.Logger

	// 本地缓存
	localCache       map[string]interface{}
	localCacheMutex  sync.RWMutex
	localCacheExpiry time.Time

	// 缓存配置
	localCacheTTL time.Duration
	redisCacheTTL time.Duration
	maxLocalKeys  int

	// 统计信息
	stats      *CacheStats
	statsMutex sync.RWMutex

	// 缓存键前缀
	keyPrefix string
}

// NewDefaultSmartCacheManager 创建默认智能缓存管理器
func NewDefaultSmartCacheManager(
	repository ExpressCompanyRepository,
	systemConfigService ConfigServiceInterface,
	redisClient *redis.Client,
	logger *zap.Logger,
) SmartCacheManager {
	return &DefaultSmartCacheManager{
		repository:          repository,
		systemConfigService: systemConfigService,
		redisClient:         redisClient,
		logger:              logger,
		localCache:          make(map[string]interface{}),
		localCacheTTL:       5 * time.Minute,  // 本地缓存5分钟
		redisCacheTTL:       30 * time.Minute, // Redis缓存30分钟
		maxLocalKeys:        1000,             // 最大本地缓存键数
		keyPrefix:           "express_cache:",
		stats: &CacheStats{
			HitCount:  0,
			MissCount: 0,
		},
	}
}

// GetCompanyStatus 获取快递公司状态（带缓存）
func (m *DefaultSmartCacheManager) GetCompanyStatus(ctx context.Context, companyCode string) (bool, error) {
	cacheKey := fmt.Sprintf("company_status:%s", companyCode)

	// 1. 尝试本地缓存
	if status, found := m.getFromLocalCache(cacheKey); found {
		m.recordCacheHit()
		if boolStatus, ok := status.(bool); ok {
			return boolStatus, nil
		}
	}

	// 2. 尝试Redis缓存
	redisKey := m.keyPrefix + cacheKey
	statusStr, err := m.redisClient.Get(ctx, redisKey).Result()
	if err == nil {
		m.recordCacheHit()
		status := statusStr == "true"
		m.setToLocalCache(cacheKey, status)
		return status, nil
	}

	// 3. 缓存未命中，从数据库查询
	m.recordCacheMiss()
	company, err := m.repository.GetCompanyByCode(companyCode)
	if err != nil {
		if err == ErrExpressCompanyNotFound {
			// 缓存不存在的结果，避免缓存穿透
			m.setToLocalCache(cacheKey, false)
			m.redisClient.Set(ctx, redisKey, "false", m.redisCacheTTL)
			return false, nil
		}
		return false, fmt.Errorf("查询快递公司状态失败: %w", err)
	}

	// 4. 更新缓存
	m.setToLocalCache(cacheKey, company.IsActive)
	m.redisClient.Set(ctx, redisKey, fmt.Sprintf("%t", company.IsActive), m.redisCacheTTL)

	m.logger.Debug("快递公司状态缓存更新",
		zap.String("company_code", companyCode),
		zap.Bool("is_active", company.IsActive))

	return company.IsActive, nil
}

// GetProviderStatus 获取供应商状态（带缓存）
func (m *DefaultSmartCacheManager) GetProviderStatus(ctx context.Context, providerCode string) (bool, error) {
	cacheKey := fmt.Sprintf("provider_status:%s", providerCode)

	// 1. 尝试本地缓存
	if status, found := m.getFromLocalCache(cacheKey); found {
		m.recordCacheHit()
		if boolStatus, ok := status.(bool); ok {
			return boolStatus, nil
		}
	}

	// 2. 尝试Redis缓存
	redisKey := m.keyPrefix + cacheKey
	statusStr, err := m.redisClient.Get(ctx, redisKey).Result()
	if err == nil {
		m.recordCacheHit()
		status := statusStr == "true"
		m.setToLocalCache(cacheKey, status)
		return status, nil
	}

	// 3. 缓存未命中，从系统配置查询
	m.recordCacheMiss()
	configKey := fmt.Sprintf("provider_%s.enabled", providerCode)
	enabled, err := m.systemConfigService.GetBoolConfig(ctx, "providers", configKey, false)
	if err != nil {
		m.logger.Error("查询供应商状态失败",
			zap.String("provider_code", providerCode),
			zap.Error(err))
		return false, fmt.Errorf("查询供应商状态失败: %w", err)
	}

	// 4. 更新缓存
	m.setToLocalCache(cacheKey, enabled)
	m.redisClient.Set(ctx, redisKey, fmt.Sprintf("%t", enabled), m.redisCacheTTL)

	m.logger.Debug("供应商状态缓存更新",
		zap.String("provider_code", providerCode),
		zap.Bool("enabled", enabled))

	return enabled, nil
}

// GetCompanyMapping 获取映射关系（带缓存）
func (m *DefaultSmartCacheManager) GetCompanyMapping(ctx context.Context, companyCode, providerCode string) (*ExpressMappingCache, error) {
	cacheKey := fmt.Sprintf("mapping:%s:%s", companyCode, providerCode)

	// 1. 尝试本地缓存
	if mapping, found := m.getFromLocalCache(cacheKey); found {
		m.recordCacheHit()
		if mappingCache, ok := mapping.(*ExpressMappingCache); ok {
			return mappingCache, nil
		}
	}

	// 2. 尝试Redis缓存
	redisKey := m.keyPrefix + cacheKey
	mappingData, err := m.redisClient.HGetAll(ctx, redisKey).Result()
	if err == nil && len(mappingData) > 0 {
		m.recordCacheHit()
		// 解析体积重量比
		volumeWeightRatio := 0
		if ratioStr, exists := mappingData["volume_weight_ratio"]; exists && ratioStr != "" {
			if ratio, parseErr := strconv.Atoi(ratioStr); parseErr == nil {
				volumeWeightRatio = ratio
			}
		}

		mapping := &ExpressMappingCache{
			CompanyCode:         mappingData["company_code"],
			CompanyName:         mappingData["company_name"],
			ProviderCode:        mappingData["provider_code"],
			ProviderCompanyCode: mappingData["provider_company_code"],
			IsSupported:         mappingData["is_supported"] == "true",
			IsPreferred:         mappingData["is_preferred"] == "true",
			VolumeWeightRatio:   volumeWeightRatio,
		}

		m.setToLocalCache(cacheKey, mapping)
		return mapping, nil
	}

	// 3. 缓存未命中，从数据库查询映射关系
	m.recordCacheMiss()
	mapping, err := m.queryMappingFromDatabase(ctx, companyCode, providerCode)
	if err != nil {
		return nil, fmt.Errorf("查询映射关系失败: %w", err)
	}

	// 4. 更新缓存
	m.setToLocalCache(cacheKey, mapping)
	m.setMappingToRedisCache(ctx, redisKey, mapping)

	return mapping, nil
}

// GetEnabledCompanies 获取启用的快递公司列表（带缓存）
func (m *DefaultSmartCacheManager) GetEnabledCompanies(ctx context.Context) ([]*ExpressCompany, error) {
	cacheKey := "enabled_companies"

	// 1. 尝试本地缓存
	if companies, found := m.getFromLocalCache(cacheKey); found {
		m.recordCacheHit()
		if companiesList, ok := companies.([]*ExpressCompany); ok {
			return companiesList, nil
		}
	}

	// 2. 缓存未命中，从数据库查询
	m.recordCacheMiss()
	isActive := true
	result, err := m.repository.GetCompanies(CompanyFilter{IsActive: &isActive}, Pagination{Page: 1, PageSize: 1000})
	if err != nil {
		return nil, fmt.Errorf("获取启用的快递公司列表失败: %w", err)
	}

	// 3. 更新缓存
	m.setToLocalCache(cacheKey, result.Companies)

	// Redis缓存使用较短的TTL，因为列表数据变化频繁
	redisKey := m.keyPrefix + cacheKey
	m.redisClient.Set(ctx, redisKey, "cached", 10*time.Minute)

	m.logger.Debug("启用快递公司列表缓存更新", zap.Int("count", len(result.Companies)))
	return result.Companies, nil
}

// GetEnabledProviders 获取启用的供应商列表（带缓存）
func (m *DefaultSmartCacheManager) GetEnabledProviders(ctx context.Context) ([]*ExpressProvider, error) {
	cacheKey := "enabled_providers"

	// 1. 尝试本地缓存
	if providers, found := m.getFromLocalCache(cacheKey); found {
		m.recordCacheHit()
		if providersList, ok := providers.([]*ExpressProvider); ok {
			return providersList, nil
		}
	}

	// 2. 缓存未命中，查询所有供应商并过滤启用的
	m.recordCacheMiss()
	result, err := m.repository.GetProviders(ProviderFilter{}, Pagination{Page: 1, PageSize: 100})
	if err != nil {
		return nil, fmt.Errorf("获取供应商列表失败: %w", err)
	}

	var enabledProviders []*ExpressProvider
	for _, provider := range result.Providers {
		enabled, err := m.GetProviderStatus(ctx, provider.Code)
		if err != nil {
			m.logger.Warn("检查供应商状态失败",
				zap.String("provider_code", provider.Code),
				zap.Error(err))
			continue
		}
		if enabled {
			enabledProviders = append(enabledProviders, provider)
		}
	}

	// 3. 更新缓存
	m.setToLocalCache(cacheKey, enabledProviders)

	m.logger.Debug("启用供应商列表缓存更新", zap.Int("count", len(enabledProviders)))
	return enabledProviders, nil
}

// InvalidateCompany 失效快递公司缓存
func (m *DefaultSmartCacheManager) InvalidateCompany(ctx context.Context, companyCode string) error {
	cacheKey := fmt.Sprintf("company_status:%s", companyCode)

	// 清除本地缓存
	m.removeFromLocalCache(cacheKey)

	// 清除Redis缓存
	redisKey := m.keyPrefix + cacheKey
	err := m.redisClient.Del(ctx, redisKey).Err()
	if err != nil {
		m.logger.Warn("清除Redis缓存失败",
			zap.String("cache_key", redisKey),
			zap.Error(err))
	}

	// 同时清除相关的列表缓存
	m.removeFromLocalCache("enabled_companies")
	m.redisClient.Del(ctx, m.keyPrefix+"enabled_companies")

	m.logger.Debug("快递公司缓存已失效", zap.String("company_code", companyCode))
	return nil
}

// InvalidateProvider 失效供应商缓存
func (m *DefaultSmartCacheManager) InvalidateProvider(ctx context.Context, providerCode string) error {
	cacheKey := fmt.Sprintf("provider_status:%s", providerCode)

	// 清除本地缓存
	m.removeFromLocalCache(cacheKey)

	// 清除Redis缓存
	redisKey := m.keyPrefix + cacheKey
	err := m.redisClient.Del(ctx, redisKey).Err()
	if err != nil {
		m.logger.Warn("清除Redis缓存失败",
			zap.String("cache_key", redisKey),
			zap.Error(err))
	}

	// 同时清除相关的列表缓存
	m.removeFromLocalCache("enabled_providers")
	m.redisClient.Del(ctx, m.keyPrefix+"enabled_providers")

	m.logger.Debug("供应商缓存已失效", zap.String("provider_code", providerCode))
	return nil
}

// InvalidateMapping 失效映射关系缓存
func (m *DefaultSmartCacheManager) InvalidateMapping(ctx context.Context, companyCode, providerCode string) error {
	cacheKey := fmt.Sprintf("mapping:%s:%s", companyCode, providerCode)

	// 清除本地缓存
	m.removeFromLocalCache(cacheKey)

	// 清除Redis缓存
	redisKey := m.keyPrefix + cacheKey
	err := m.redisClient.Del(ctx, redisKey).Err()
	if err != nil {
		m.logger.Warn("清除Redis缓存失败",
			zap.String("cache_key", redisKey),
			zap.Error(err))
	}

	m.logger.Debug("映射关系缓存已失效",
		zap.String("company_code", companyCode),
		zap.String("provider_code", providerCode))
	return nil
}

// WarmupCache 预热缓存
func (m *DefaultSmartCacheManager) WarmupCache(ctx context.Context) error {
	startTime := time.Now()
	m.logger.Info("开始缓存预热")

	// 1. 预热快递公司状态
	companies, err := m.GetEnabledCompanies(ctx)
	if err != nil {
		return fmt.Errorf("预热快递公司缓存失败: %w", err)
	}

	for _, company := range companies {
		_, err := m.GetCompanyStatus(ctx, company.Code)
		if err != nil {
			m.logger.Warn("预热快递公司状态失败",
				zap.String("company_code", company.Code),
				zap.Error(err))
		}
	}

	// 2. 预热供应商状态
	providers, err := m.GetEnabledProviders(ctx)
	if err != nil {
		return fmt.Errorf("预热供应商缓存失败: %w", err)
	}

	for _, provider := range providers {
		_, err := m.GetProviderStatus(ctx, provider.Code)
		if err != nil {
			m.logger.Warn("预热供应商状态失败",
				zap.String("provider_code", provider.Code),
				zap.Error(err))
		}
	}

	duration := time.Since(startTime)
	m.logger.Info("缓存预热完成",
		zap.Duration("duration", duration),
		zap.Int("companies_count", len(companies)),
		zap.Int("providers_count", len(providers)))

	// 更新统计信息
	m.statsMutex.Lock()
	m.stats.LastWarmupTime = time.Now().Format("2006-01-02 15:04:05")
	m.statsMutex.Unlock()

	return nil
}

// GetCacheStats 获取缓存统计信息
func (m *DefaultSmartCacheManager) GetCacheStats(ctx context.Context) (*CacheStats, error) {
	m.statsMutex.RLock()
	defer m.statsMutex.RUnlock()

	// 计算命中率
	total := m.stats.HitCount + m.stats.MissCount
	if total > 0 {
		m.stats.HitRate = float64(m.stats.HitCount) / float64(total) * 100
	}

	// 获取本地缓存键数量
	m.localCacheMutex.RLock()
	m.stats.TotalKeys = len(m.localCache)
	m.localCacheMutex.RUnlock()

	// 设置缓存过期时间信息
	m.stats.CacheExpiry = m.localCacheExpiry.Format("2006-01-02 15:04:05")

	// 复制统计信息以避免并发问题
	statsCopy := &CacheStats{
		HitCount:       m.stats.HitCount,
		MissCount:      m.stats.MissCount,
		HitRate:        m.stats.HitRate,
		TotalKeys:      m.stats.TotalKeys,
		MemoryUsage:    m.stats.MemoryUsage,
		LastWarmupTime: m.stats.LastWarmupTime,
		CacheExpiry:    m.stats.CacheExpiry,
	}

	return statsCopy, nil
}

// ==================== 私有辅助方法 ====================

// getFromLocalCache 从本地缓存获取数据
func (m *DefaultSmartCacheManager) getFromLocalCache(key string) (interface{}, bool) {
	m.localCacheMutex.RLock()
	defer m.localCacheMutex.RUnlock()

	// 检查缓存是否过期
	if time.Now().After(m.localCacheExpiry) {
		return nil, false
	}

	value, found := m.localCache[key]
	return value, found
}

// setToLocalCache 设置数据到本地缓存
func (m *DefaultSmartCacheManager) setToLocalCache(key string, value interface{}) {
	m.localCacheMutex.Lock()
	defer m.localCacheMutex.Unlock()

	// 检查缓存大小限制
	if len(m.localCache) >= m.maxLocalKeys {
		// 简单的LRU策略：清空一半缓存
		for k := range m.localCache {
			delete(m.localCache, k)
			if len(m.localCache) <= m.maxLocalKeys/2 {
				break
			}
		}
		m.logger.Debug("本地缓存已清理", zap.Int("remaining_keys", len(m.localCache)))
	}

	m.localCache[key] = value

	// 更新缓存过期时间
	if m.localCacheExpiry.IsZero() {
		m.localCacheExpiry = time.Now().Add(m.localCacheTTL)
	}
}

// removeFromLocalCache 从本地缓存移除数据
func (m *DefaultSmartCacheManager) removeFromLocalCache(key string) {
	m.localCacheMutex.Lock()
	defer m.localCacheMutex.Unlock()

	delete(m.localCache, key)
}

// recordCacheHit 记录缓存命中
func (m *DefaultSmartCacheManager) recordCacheHit() {
	m.statsMutex.Lock()
	defer m.statsMutex.Unlock()

	m.stats.HitCount++
}

// recordCacheMiss 记录缓存未命中
func (m *DefaultSmartCacheManager) recordCacheMiss() {
	m.statsMutex.Lock()
	defer m.statsMutex.Unlock()

	m.stats.MissCount++
}

// queryMappingFromDatabase 从数据库查询映射关系
func (m *DefaultSmartCacheManager) queryMappingFromDatabase(ctx context.Context, companyCode, providerCode string) (*ExpressMappingCache, error) {
	// 1. 获取快递公司信息
	company, err := m.repository.GetCompanyByCode(companyCode)
	if err != nil {
		if err == ErrExpressCompanyNotFound {
			return nil, fmt.Errorf("快递公司不存在: %s", companyCode)
		}
		return nil, fmt.Errorf("获取快递公司信息失败: %w", err)
	}

	// 2. 获取供应商信息
	provider, err := m.repository.GetProviderByCode(providerCode)
	if err != nil {
		if err == ErrExpressProviderNotFound {
			return nil, fmt.Errorf("供应商不存在: %s", providerCode)
		}
		return nil, fmt.Errorf("获取供应商信息失败: %w", err)
	}

	// 3. 获取映射关系
	mappingRecord, err := m.repository.GetMapping(company.ID, provider.ID)
	if err != nil {
		if err == ErrExpressMappingNotFound {
			// 返回默认的不支持映射
			return &ExpressMappingCache{
				CompanyCode:         companyCode,
				CompanyName:         company.Name,
				ProviderCode:        providerCode,
				ProviderCompanyCode: "",
				IsSupported:         false,
				IsPreferred:         false,
				VolumeWeightRatio:   8000, // 默认抛比
			}, nil
		}
		return nil, fmt.Errorf("获取映射关系失败: %w", err)
	}

	// 4. 获取体积重量比（从快递公司配置中获取，默认8000）
	volumeWeightRatio := 8000
	if company.Code == "JD" {
		volumeWeightRatio = 8000
	} else if company.Code == "DBL" {
		volumeWeightRatio = 6000
	}

	// 5. 构建缓存对象
	mapping := &ExpressMappingCache{
		CompanyCode:         companyCode,
		CompanyName:         company.Name,
		ProviderCode:        providerCode,
		ProviderCompanyCode: mappingRecord.ProviderCompanyCode,
		IsSupported:         mappingRecord.IsSupported,
		IsPreferred:         mappingRecord.IsPreferred,
		VolumeWeightRatio:   volumeWeightRatio,
	}

	// 6. 解析产品类型映射（从JSON字节数组解析）
	if mappingRecord.ProductTypeMappings != nil && len(mappingRecord.ProductTypeMappings) > 0 {
		var productMappings map[string]string
		if err := json.Unmarshal(mappingRecord.ProductTypeMappings, &productMappings); err == nil {
			mapping.ProductTypeMappings = productMappings
		} else {
			m.logger.Warn("解析产品类型映射失败",
				zap.String("company_code", companyCode),
				zap.String("provider_code", providerCode),
				zap.Error(err))
		}
	}

	// 7. 解析支付方式映射（从JSON字节数组解析）
	if mappingRecord.PayMethodMappings != nil && len(mappingRecord.PayMethodMappings) > 0 {
		var payMappings map[string]string
		if err := json.Unmarshal(mappingRecord.PayMethodMappings, &payMappings); err == nil {
			mapping.PayMethodMappings = payMappings
		} else {
			m.logger.Warn("解析支付方式映射失败",
				zap.String("company_code", companyCode),
				zap.String("provider_code", providerCode),
				zap.Error(err))
		}
	}

	m.logger.Debug("从数据库查询映射关系成功",
		zap.String("company_code", companyCode),
		zap.String("provider_code", providerCode),
		zap.Bool("is_supported", mapping.IsSupported))

	return mapping, nil
}

// setMappingToRedisCache 设置映射关系到Redis缓存
func (m *DefaultSmartCacheManager) setMappingToRedisCache(ctx context.Context, redisKey string, mapping *ExpressMappingCache) {
	// 构建Redis Hash数据
	mappingData := map[string]interface{}{
		"company_code":          mapping.CompanyCode,
		"company_name":          mapping.CompanyName,
		"provider_code":         mapping.ProviderCode,
		"provider_company_code": mapping.ProviderCompanyCode,
		"is_supported":          fmt.Sprintf("%t", mapping.IsSupported),
		"is_preferred":          fmt.Sprintf("%t", mapping.IsPreferred),
		"volume_weight_ratio":   fmt.Sprintf("%d", mapping.VolumeWeightRatio),
	}

	// 设置到Redis
	err := m.redisClient.HMSet(ctx, redisKey, mappingData).Err()
	if err != nil {
		m.logger.Warn("设置映射关系到Redis缓存失败",
			zap.String("redis_key", redisKey),
			zap.Error(err))
		return
	}

	// 设置过期时间
	m.redisClient.Expire(ctx, redisKey, m.redisCacheTTL)

	m.logger.Debug("映射关系已缓存到Redis",
		zap.String("redis_key", redisKey),
		zap.Duration("ttl", m.redisCacheTTL))
}
