package express

import (
	"context"
	"fmt"
	"sync"
	"time"

go.uber.org/zap
)

// SystemConfigService 系统配置服务接口（避免循环依赖）
type SystemConfigService interface {
	GetBoolConfig(ctx context.Context, group, key string, defaultValue bool) (bool, error)
	CreateConfig(ctx context.Context, req *SystemConfigRequest, operatorID string) (*SystemConfig, error)
	UpdateConfig(ctx context.Context, id string, req *SystemConfigRequest, operatorID string) (*SystemConfig, error)
	GetConfigByKey(ctx context.Context, group, key string) (*SystemConfig, error)
}

// SystemConfig 系统配置模型（避免循环依赖）
type SystemConfig struct {
	ID          string `json:"id"`
	ConfigGroup string `json:"config_group"`
	ConfigKey   string `json:"config_key"`
	ConfigValue string `json:"config_value"`
	ConfigType  string `json:"config_type"`
	Description string `json:"description"`
}

// SystemConfigRequest 系统配置请求（避免循环依赖）
type SystemConfigRequest struct {
	ConfigGroup  string
	ConfigKey    string
	ConfigValue  string
	ConfigType   string
	Description  string
	ChangeReason string
}

// ExpressCompanyStatusManager 快递公司状态管理器
// 提供统一的状态检查、变更通知和缓存管理功能
type ExpressCompanyStatusManager interface {
	// IsCompanyEnabled 检查快递公司是否启用
	IsCompanyEnabled(ctx context.Context, companyCode string) (bool, error)

	// IsProviderEnabled 检查供应商是否启用
	IsProviderEnabled(ctx context.Context, providerCode string) (bool, error)

	// IsCompanyEnabledForProvider 检查快递公司在特定供应商下是否启用
	IsCompanyEnabledForProvider(ctx context.Context, companyCode, providerCode string) (bool, error)

	// GetEnabledCompanies 获取启用的快递公司列表
	GetEnabledCompanies(ctx context.Context) ([]*ExpressCompany, error)

	// GetEnabledProviders 获取启用的供应商列表
	GetEnabledProviders(ctx context.Context) ([]*ExpressProvider, error)

	// GetEnabledCompaniesForProvider 获取供应商支持的启用快递公司列表
	GetEnabledCompaniesForProvider(ctx context.Context, providerCode string) ([]*ExpressCompany, error)

	// UpdateCompanyStatus 更新快递公司状态
	UpdateCompanyStatus(ctx context.Context, companyCode string, enabled bool, operatorID string) error

	// UpdateProviderStatus 更新供应商状态
	UpdateProviderStatus(ctx context.Context, providerCode string, enabled bool, operatorID string) error

	// UpdateCompanyProviderMapping 更新快递公司供应商映射状态
	UpdateCompanyProviderMapping(ctx context.Context, companyCode, providerCode string, enabled bool, operatorID string) error

	// RefreshCache 刷新状态缓存
	RefreshCache(ctx context.Context) error

	// RegisterStatusChangeListener 注册状态变更监听器
	RegisterStatusChangeListener(listener StatusChangeListener)
}

// StatusChangeListener 状态变更监听器接口
type StatusChangeListener interface {
	OnCompanyStatusChanged(ctx context.Context, companyCode string, enabled bool)
	OnProviderStatusChanged(ctx context.Context, providerCode string, enabled bool)
	OnMappingStatusChanged(ctx context.Context, companyCode, providerCode string, enabled bool)
}

// StatusChangeEvent 状态变更事件
type StatusChangeEvent struct {
	Type         StatusChangeType `json:"type"`
	CompanyCode  string           `json:"company_code,omitempty"`
	ProviderCode string           `json:"provider_code,omitempty"`
	Enabled      bool             `json:"enabled"`
	OperatorID   string           `json:"operator_id"`
	Timestamp    time.Time        `json:"timestamp"`
}

// StatusChangeType 状态变更类型
type StatusChangeType string

const (
	StatusChangeTypeCompany  StatusChangeType = "company"
	StatusChangeTypeProvider StatusChangeType = "provider"
	StatusChangeTypeMapping  StatusChangeType = "mapping"
)

// DefaultExpressCompanyStatusManager 默认快递公司状态管理器实现
type DefaultExpressCompanyStatusManager struct {
	repository          ExpressCompanyRepository
	systemConfigService SystemConfigService
	cacheService        *ExpressMappingCacheService
	logger              *zap.Logger

	// 状态缓存
	companyStatusCache  map[string]bool
	providerStatusCache map[string]bool
	mappingStatusCache  map[string]map[string]bool // companyCode -> providerCode -> enabled
	cacheMutex          sync.RWMutex

	// 事件监听器
	listeners      []StatusChangeListener
	listenersMutex sync.RWMutex

	// 缓存过期时间
	cacheExpiry time.Time
	cacheTTL    time.Duration

	// 性能优化配置
	enableLocalCache bool
	maxCacheSize     int
	cacheHitCount    int64
	cacheMissCount   int64
}

// NewDefaultExpressCompanyStatusManager 创建默认快递公司状态管理器
func NewDefaultExpressCompanyStatusManager(
	repository ExpressCompanyRepository,
	systemConfigService service.SystemConfigService,
	cacheService *ExpressMappingCacheService,
	logger *zap.Logger,
) ExpressCompanyStatusManager {
	return &DefaultExpressCompanyStatusManager{
		repository:          repository,
		systemConfigService: systemConfigService,
		cacheService:        cacheService,
		logger:              logger,
		companyStatusCache:  make(map[string]bool),
		providerStatusCache: make(map[string]bool),
		mappingStatusCache:  make(map[string]map[string]bool),
		listeners:           make([]StatusChangeListener, 0),
		cacheTTL:            5 * time.Minute, // 缓存5分钟
		enableLocalCache:    true,            // 启用本地缓存
		maxCacheSize:        1000,            // 最大缓存条目数
		cacheHitCount:       0,
		cacheMissCount:      0,
	}
}

// IsCompanyEnabled 检查快递公司是否启用
func (m *DefaultExpressCompanyStatusManager) IsCompanyEnabled(ctx context.Context, companyCode string) (bool, error) {
	// 先检查缓存
	if enabled, found := m.getCompanyStatusFromCache(companyCode); found {
		return enabled, nil
	}

	// 缓存未命中，从数据库查询
	company, err := m.repository.GetCompanyByCode(companyCode)
	if err != nil {
		if err == ErrExpressCompanyNotFound {
			m.logger.Warn("快递公司不存在", zap.String("company_code", companyCode))
			return false, nil
		}
		return false, fmt.Errorf("查询快递公司状态失败: %w", err)
	}

	// 更新缓存
	m.setCompanyStatusToCache(companyCode, company.IsActive)

	m.logger.Debug("快递公司状态查询",
		zap.String("company_code", companyCode),
		zap.String("company_name", company.Name),
		zap.Bool("is_active", company.IsActive))

	return company.IsActive, nil
}

// IsProviderEnabled 检查供应商是否启用
func (m *DefaultExpressCompanyStatusManager) IsProviderEnabled(ctx context.Context, providerCode string) (bool, error) {
	// 先检查缓存
	if enabled, found := m.getProviderStatusFromCache(providerCode); found {
		return enabled, nil
	}

	// 缓存未命中，从系统配置查询
	configKey := fmt.Sprintf("provider_%s.enabled", providerCode)
	enabled, err := m.systemConfigService.GetBoolConfig(ctx, "providers", configKey, false)
	if err != nil {
		m.logger.Error("查询供应商状态失败",
			zap.String("provider_code", providerCode),
			zap.String("config_key", configKey),
			zap.Error(err))
		return false, fmt.Errorf("查询供应商状态失败: %w", err)
	}

	// 更新缓存
	m.setProviderStatusToCache(providerCode, enabled)

	m.logger.Debug("供应商状态查询",
		zap.String("provider_code", providerCode),
		zap.Bool("enabled", enabled))

	return enabled, nil
}

// IsCompanyEnabledForProvider 检查快递公司在特定供应商下是否启用
func (m *DefaultExpressCompanyStatusManager) IsCompanyEnabledForProvider(ctx context.Context, companyCode, providerCode string) (bool, error) {
	// 1. 检查供应商是否启用
	providerEnabled, err := m.IsProviderEnabled(ctx, providerCode)
	if err != nil {
		return false, err
	}
	if !providerEnabled {
		return false, nil
	}

	// 2. 检查快递公司是否启用
	companyEnabled, err := m.IsCompanyEnabled(ctx, companyCode)
	if err != nil {
		return false, err
	}
	if !companyEnabled {
		return false, nil
	}

	// 3. 检查映射关系是否支持
	mapping, err := m.cacheService.GetCompanyMapping(ctx, companyCode, providerCode)
	if err != nil {
		m.logger.Warn("获取映射关系失败",
			zap.String("company_code", companyCode),
			zap.String("provider_code", providerCode),
			zap.Error(err))
		return false, nil
	}

	return mapping.IsSupported, nil
}

// GetEnabledCompanies 获取启用的快递公司列表
func (m *DefaultExpressCompanyStatusManager) GetEnabledCompanies(ctx context.Context) ([]*ExpressCompany, error) {
	isActive := true
	result, err := m.repository.GetCompanies(CompanyFilter{IsActive: &isActive}, Pagination{Page: 1, PageSize: 1000})
	if err != nil {
		return nil, fmt.Errorf("获取启用的快递公司列表失败: %w", err)
	}

	m.logger.Debug("获取启用的快递公司列表", zap.Int("count", len(result.Companies)))
	return result.Companies, nil
}

// GetEnabledProviders 获取启用的供应商列表
func (m *DefaultExpressCompanyStatusManager) GetEnabledProviders(ctx context.Context) ([]*ExpressProvider, error) {
	// 获取所有供应商
	result, err := m.repository.GetProviders(ProviderFilter{}, Pagination{Page: 1, PageSize: 100})
	if err != nil {
		return nil, fmt.Errorf("获取供应商列表失败: %w", err)
	}

	var enabledProviders []*ExpressProvider
	for _, provider := range result.Providers {
		enabled, err := m.IsProviderEnabled(ctx, provider.Code)
		if err != nil {
			m.logger.Warn("检查供应商状态失败",
				zap.String("provider_code", provider.Code),
				zap.Error(err))
			continue
		}
		if enabled {
			enabledProviders = append(enabledProviders, provider)
		}
	}

	m.logger.Debug("获取启用的供应商列表", zap.Int("count", len(enabledProviders)))
	return enabledProviders, nil
}

// GetEnabledCompaniesForProvider 获取供应商支持的启用快递公司列表
func (m *DefaultExpressCompanyStatusManager) GetEnabledCompaniesForProvider(ctx context.Context, providerCode string) ([]*ExpressCompany, error) {
	// 1. 检查供应商是否启用
	providerEnabled, err := m.IsProviderEnabled(ctx, providerCode)
	if err != nil {
		return nil, err
	}
	if !providerEnabled {
		return []*ExpressCompany{}, nil
	}

	// 2. 获取供应商支持的映射关系
	mappings, err := m.cacheService.GetSupportedCompanies(ctx, providerCode)
	if err != nil {
		return nil, fmt.Errorf("获取供应商支持的快递公司失败: %w", err)
	}

	var enabledCompanies []*ExpressCompany
	for _, mapping := range mappings {
		if !mapping.IsSupported {
			continue
		}

		// 检查快递公司是否启用
		companyEnabled, err := m.IsCompanyEnabled(ctx, mapping.CompanyCode)
		if err != nil {
			m.logger.Warn("检查快递公司状态失败",
				zap.String("company_code", mapping.CompanyCode),
				zap.Error(err))
			continue
		}

		if companyEnabled {
			// 获取完整的快递公司信息
			company, err := m.repository.GetCompanyByCode(mapping.CompanyCode)
			if err != nil {
				m.logger.Warn("获取快递公司信息失败",
					zap.String("company_code", mapping.CompanyCode),
					zap.Error(err))
				continue
			}
			enabledCompanies = append(enabledCompanies, company)
		}
	}

	m.logger.Debug("获取供应商支持的启用快递公司列表",
		zap.String("provider_code", providerCode),
		zap.Int("count", len(enabledCompanies)))

	return enabledCompanies, nil
}

// UpdateCompanyStatus 更新快递公司状态
func (m *DefaultExpressCompanyStatusManager) UpdateCompanyStatus(ctx context.Context, companyCode string, enabled bool, operatorID string) error {
	// 1. 获取快递公司信息
	company, err := m.repository.GetCompanyByCode(companyCode)
	if err != nil {
		if err == ErrExpressCompanyNotFound {
			return fmt.Errorf("快递公司不存在: %s", companyCode)
		}
		return fmt.Errorf("获取快递公司信息失败: %w", err)
	}

	// 2. 检查状态是否需要更新
	if company.IsActive == enabled {
		m.logger.Info("快递公司状态无需更新",
			zap.String("company_code", companyCode),
			zap.Bool("current_status", company.IsActive),
			zap.Bool("target_status", enabled))
		return nil
	}

	// 3. 更新数据库
	company.IsActive = enabled
	company.UpdatedBy = &operatorID
	err = m.repository.UpdateCompany(company)
	if err != nil {
		return fmt.Errorf("更新快递公司状态失败: %w", err)
	}

	// 4. 清除缓存
	m.clearCompanyStatusCache(companyCode)

	// 5. 刷新映射缓存
	if err := m.cacheService.RefreshCache(ctx); err != nil {
		m.logger.Warn("刷新映射缓存失败", zap.Error(err))
	}

	// 6. 通知监听器
	m.notifyStatusChange(ctx, StatusChangeEvent{
		Type:        StatusChangeTypeCompany,
		CompanyCode: companyCode,
		Enabled:     enabled,
		OperatorID:  operatorID,
		Timestamp:   time.Now(),
	})

	m.logger.Info("快递公司状态更新成功",
		zap.String("company_code", companyCode),
		zap.String("company_name", company.Name),
		zap.Bool("enabled", enabled),
		zap.String("operator_id", operatorID))

	return nil
}

// UpdateProviderStatus 更新供应商状态
func (m *DefaultExpressCompanyStatusManager) UpdateProviderStatus(ctx context.Context, providerCode string, enabled bool, operatorID string) error {
	// 1. 获取现有配置
	configKey := fmt.Sprintf("provider_%s.enabled", providerCode)
	existingConfig, err := m.systemConfigService.GetConfigByKey(ctx, "providers", configKey)

	if err != nil {
		// 配置不存在，创建新配置
		req := &SystemConfigRequest{
			ConfigGroup:  "providers",
			ConfigKey:    configKey,
			ConfigValue:  fmt.Sprintf("%t", enabled),
			ConfigType:   "boolean",
			Description:  fmt.Sprintf("供应商 %s 启用状态", providerCode),
			ChangeReason: "创建供应商启用状态配置",
		}
		_, err = m.systemConfigService.CreateConfig(ctx, req, operatorID)
		if err != nil {
			return fmt.Errorf("创建供应商状态配置失败: %w", err)
		}
	} else {
		// 配置存在，更新配置
		req := &SystemConfigRequest{
			ConfigGroup:  existingConfig.ConfigGroup,
			ConfigKey:    existingConfig.ConfigKey,
			ConfigValue:  fmt.Sprintf("%t", enabled),
			ConfigType:   existingConfig.ConfigType,
			Description:  existingConfig.Description,
			ChangeReason: "更新供应商启用状态",
		}
		_, err = m.systemConfigService.UpdateConfig(ctx, existingConfig.ID, req, operatorID)
		if err != nil {
			return fmt.Errorf("更新供应商状态失败: %w", err)
		}
	}

	// 2. 清除缓存
	m.clearProviderStatusCache(providerCode)

	// 3. 刷新映射缓存
	if err := m.cacheService.RefreshCache(ctx); err != nil {
		m.logger.Warn("刷新映射缓存失败", zap.Error(err))
	}

	// 4. 通知监听器
	m.notifyStatusChange(ctx, StatusChangeEvent{
		Type:         StatusChangeTypeProvider,
		ProviderCode: providerCode,
		Enabled:      enabled,
		OperatorID:   operatorID,
		Timestamp:    time.Now(),
	})

	m.logger.Info("供应商状态更新成功",
		zap.String("provider_code", providerCode),
		zap.Bool("enabled", enabled),
		zap.String("operator_id", operatorID))

	return nil
}

// UpdateCompanyProviderMapping 更新快递公司供应商映射状态
func (m *DefaultExpressCompanyStatusManager) UpdateCompanyProviderMapping(ctx context.Context, companyCode, providerCode string, enabled bool, operatorID string) error {
	// 1. 获取映射关系
	mapping, err := m.cacheService.GetCompanyMapping(ctx, companyCode, providerCode)
	if err != nil {
		return fmt.Errorf("获取映射关系失败: %w", err)
	}

	// 2. 检查状态是否需要更新
	if mapping.IsSupported == enabled {
		m.logger.Info("映射关系状态无需更新",
			zap.String("company_code", companyCode),
			zap.String("provider_code", providerCode),
			zap.Bool("current_status", mapping.IsSupported),
			zap.Bool("target_status", enabled))
		return nil
	}

	// 3. 获取快递公司和供应商信息
	company, err := m.repository.GetCompanyByCode(companyCode)
	if err != nil {
		return fmt.Errorf("获取快递公司信息失败: %w", err)
	}

	provider, err := m.repository.GetProviderByCode(providerCode)
	if err != nil {
		return fmt.Errorf("获取供应商信息失败: %w", err)
	}

	// 4. 获取映射记录
	mappingRecord, err := m.repository.GetMapping(company.ID, provider.ID)
	if err != nil {
		return fmt.Errorf("获取映射记录失败: %w", err)
	}

	// 5. 更新映射关系
	mappingRecord.IsSupported = enabled
	mappingRecord.UpdatedBy = &operatorID
	err = m.repository.UpdateMapping(mappingRecord)
	if err != nil {
		return fmt.Errorf("更新映射关系失败: %w", err)
	}

	// 6. 清除相关缓存
	m.clearMappingStatusCache(companyCode, providerCode)

	// 7. 刷新映射缓存
	if err := m.cacheService.RefreshCache(ctx); err != nil {
		m.logger.Warn("刷新映射缓存失败", zap.Error(err))
	}

	// 8. 通知监听器
	m.notifyStatusChange(ctx, StatusChangeEvent{
		Type:         StatusChangeTypeMapping,
		CompanyCode:  companyCode,
		ProviderCode: providerCode,
		Enabled:      enabled,
		OperatorID:   operatorID,
		Timestamp:    time.Now(),
	})

	m.logger.Info("映射关系状态更新成功",
		zap.String("company_code", companyCode),
		zap.String("provider_code", providerCode),
		zap.Bool("enabled", enabled),
		zap.String("operator_id", operatorID))

	return nil
}

// RefreshCache 刷新状态缓存
func (m *DefaultExpressCompanyStatusManager) RefreshCache(ctx context.Context) error {
	m.cacheMutex.Lock()
	defer m.cacheMutex.Unlock()

	// 清空所有缓存
	m.companyStatusCache = make(map[string]bool)
	m.providerStatusCache = make(map[string]bool)
	m.mappingStatusCache = make(map[string]map[string]bool)
	m.cacheExpiry = time.Time{} // 重置过期时间

	// 刷新映射缓存
	if err := m.cacheService.RefreshCache(ctx); err != nil {
		return fmt.Errorf("刷新映射缓存失败: %w", err)
	}

	m.logger.Info("状态缓存刷新成功")
	return nil
}

// RegisterStatusChangeListener 注册状态变更监听器
func (m *DefaultExpressCompanyStatusManager) RegisterStatusChangeListener(listener StatusChangeListener) {
	m.listenersMutex.Lock()
	defer m.listenersMutex.Unlock()

	m.listeners = append(m.listeners, listener)
	m.logger.Info("状态变更监听器注册成功", zap.Int("total_listeners", len(m.listeners)))
}

// ==================== 私有辅助方法 ====================

// getCompanyStatusFromCache 从缓存获取快递公司状态
func (m *DefaultExpressCompanyStatusManager) getCompanyStatusFromCache(companyCode string) (bool, bool) {
	m.cacheMutex.RLock()
	defer m.cacheMutex.RUnlock()

	// 检查缓存是否过期
	if time.Now().After(m.cacheExpiry) {
		return false, false
	}

	enabled, found := m.companyStatusCache[companyCode]
	return enabled, found
}

// setCompanyStatusToCache 设置快递公司状态到缓存
func (m *DefaultExpressCompanyStatusManager) setCompanyStatusToCache(companyCode string, enabled bool) {
	m.cacheMutex.Lock()
	defer m.cacheMutex.Unlock()

	m.companyStatusCache[companyCode] = enabled
	// 更新缓存过期时间
	if m.cacheExpiry.IsZero() {
		m.cacheExpiry = time.Now().Add(m.cacheTTL)
	}
}

// clearCompanyStatusCache 清除快递公司状态缓存
func (m *DefaultExpressCompanyStatusManager) clearCompanyStatusCache(companyCode string) {
	m.cacheMutex.Lock()
	defer m.cacheMutex.Unlock()

	delete(m.companyStatusCache, companyCode)
}

// getProviderStatusFromCache 从缓存获取供应商状态
func (m *DefaultExpressCompanyStatusManager) getProviderStatusFromCache(providerCode string) (bool, bool) {
	m.cacheMutex.RLock()
	defer m.cacheMutex.RUnlock()

	// 检查缓存是否过期
	if time.Now().After(m.cacheExpiry) {
		return false, false
	}

	enabled, found := m.providerStatusCache[providerCode]
	return enabled, found
}

// setProviderStatusToCache 设置供应商状态到缓存
func (m *DefaultExpressCompanyStatusManager) setProviderStatusToCache(providerCode string, enabled bool) {
	m.cacheMutex.Lock()
	defer m.cacheMutex.Unlock()

	m.providerStatusCache[providerCode] = enabled
	// 更新缓存过期时间
	if m.cacheExpiry.IsZero() {
		m.cacheExpiry = time.Now().Add(m.cacheTTL)
	}
}

// clearProviderStatusCache 清除供应商状态缓存
func (m *DefaultExpressCompanyStatusManager) clearProviderStatusCache(providerCode string) {
	m.cacheMutex.Lock()
	defer m.cacheMutex.Unlock()

	delete(m.providerStatusCache, providerCode)
}

// clearMappingStatusCache 清除映射关系状态缓存
func (m *DefaultExpressCompanyStatusManager) clearMappingStatusCache(companyCode, providerCode string) {
	m.cacheMutex.Lock()
	defer m.cacheMutex.Unlock()

	if providerMap, exists := m.mappingStatusCache[companyCode]; exists {
		delete(providerMap, providerCode)
		if len(providerMap) == 0 {
			delete(m.mappingStatusCache, companyCode)
		}
	}
}

// notifyStatusChange 通知状态变更监听器
func (m *DefaultExpressCompanyStatusManager) notifyStatusChange(ctx context.Context, event StatusChangeEvent) {
	m.listenersMutex.RLock()
	listeners := make([]StatusChangeListener, len(m.listeners))
	copy(listeners, m.listeners)
	m.listenersMutex.RUnlock()

	// 异步通知所有监听器
	for _, listener := range listeners {
		go func(l StatusChangeListener) {
			defer func() {
				if r := recover(); r != nil {
					m.logger.Error("状态变更监听器执行失败",
						zap.Any("event", event),
						zap.Any("panic", r))
				}
			}()

			switch event.Type {
			case StatusChangeTypeCompany:
				l.OnCompanyStatusChanged(ctx, event.CompanyCode, event.Enabled)
			case StatusChangeTypeProvider:
				l.OnProviderStatusChanged(ctx, event.ProviderCode, event.Enabled)
			case StatusChangeTypeMapping:
				l.OnMappingStatusChanged(ctx, event.CompanyCode, event.ProviderCode, event.Enabled)
			}
		}(listener)
	}

	m.logger.Debug("状态变更事件已通知",
		zap.Any("event", event),
		zap.Int("listener_count", len(listeners)))
}

// ==================== 🚀 企业级增强功能 ====================

// GetCacheStatistics 获取缓存统计信息
func (m *DefaultExpressCompanyStatusManager) GetCacheStatistics() map[string]interface{} {
	m.cacheMutex.RLock()
	defer m.cacheMutex.RUnlock()

	totalRequests := m.cacheHitCount + m.cacheMissCount
	hitRate := 0.0
	if totalRequests > 0 {
		hitRate = float64(m.cacheHitCount) / float64(totalRequests) * 100
	}

	return map[string]interface{}{
		"cache_hit_count":     m.cacheHitCount,
		"cache_miss_count":    m.cacheMissCount,
		"cache_hit_rate":      hitRate,
		"company_cache_size":  len(m.companyStatusCache),
		"provider_cache_size": len(m.providerStatusCache),
		"mapping_cache_size":  len(m.mappingStatusCache),
		"cache_expiry":        m.cacheExpiry.Format("2006-01-02 15:04:05"),
		"cache_ttl_minutes":   m.cacheTTL.Minutes(),
		"listeners_count":     len(m.listeners),
		"local_cache_enabled": m.enableLocalCache,
		"max_cache_size":      m.maxCacheSize,
	}
}

// WarmupCache 预热缓存
func (m *DefaultExpressCompanyStatusManager) WarmupCache(ctx context.Context) error {
	startTime := time.Now()
	m.logger.Info("开始状态管理器缓存预热")

	// 1. 预热快递公司状态
	companies, err := m.GetEnabledCompanies(ctx)
	if err != nil {
		return fmt.Errorf("预热快递公司缓存失败: %w", err)
	}

	for _, company := range companies {
		_, err := m.IsCompanyEnabled(ctx, company.Code)
		if err != nil {
			m.logger.Warn("预热快递公司状态失败",
				zap.String("company_code", company.Code),
				zap.Error(err))
		}
	}

	// 2. 预热供应商状态
	providers, err := m.GetEnabledProviders(ctx)
	if err != nil {
		return fmt.Errorf("预热供应商缓存失败: %w", err)
	}

	for _, provider := range providers {
		_, err := m.IsProviderEnabled(ctx, provider.Code)
		if err != nil {
			m.logger.Warn("预热供应商状态失败",
				zap.String("provider_code", provider.Code),
				zap.Error(err))
		}
	}

	duration := time.Since(startTime)
	m.logger.Info("状态管理器缓存预热完成",
		zap.Duration("duration", duration),
		zap.Int("companies_count", len(companies)),
		zap.Int("providers_count", len(providers)))

	return nil
}

// ClearCache 清空所有缓存
func (m *DefaultExpressCompanyStatusManager) ClearCache() {
	m.cacheMutex.Lock()
	defer m.cacheMutex.Unlock()

	m.companyStatusCache = make(map[string]bool)
	m.providerStatusCache = make(map[string]bool)
	m.mappingStatusCache = make(map[string]map[string]bool)
	m.cacheExpiry = time.Time{}

	m.logger.Info("状态管理器缓存已清空")
}

// ValidateConfiguration 验证配置完整性
func (m *DefaultExpressCompanyStatusManager) ValidateConfiguration(ctx context.Context) error {
	// 1. 验证仓储接口
	if m.repository == nil {
		return fmt.Errorf("ExpressCompanyRepository 未配置")
	}

	// 2. 验证系统配置服务
	if m.systemConfigService == nil {
		return fmt.Errorf("SystemConfigService 未配置")
	}

	// 3. 验证缓存服务
	if m.cacheService == nil {
		return fmt.Errorf("ExpressMappingCacheService 未配置")
	}

	// 4. 验证日志器
	if m.logger == nil {
		return fmt.Errorf("Logger 未配置")
	}

	// 5. 测试数据库连接
	_, err := m.repository.GetCompanies(CompanyFilter{}, Pagination{Page: 1, PageSize: 1})
	if err != nil {
		return fmt.Errorf("数据库连接测试失败: %w", err)
	}

	m.logger.Info("状态管理器配置验证通过")
	return nil
}

// recordCacheHit 记录缓存命中（线程安全）
func (m *DefaultExpressCompanyStatusManager) recordCacheHit() {
	if m.enableLocalCache {
		m.cacheMutex.Lock()
		m.cacheHitCount++
		m.cacheMutex.Unlock()
	}
}

// recordCacheMiss 记录缓存未命中（线程安全）
func (m *DefaultExpressCompanyStatusManager) recordCacheMiss() {
	if m.enableLocalCache {
		m.cacheMutex.Lock()
		m.cacheMissCount++
		m.cacheMutex.Unlock()
	}
}

// optimizeCache 优化缓存性能
func (m *DefaultExpressCompanyStatusManager) optimizeCache() {
	m.cacheMutex.Lock()
	defer m.cacheMutex.Unlock()

	// 如果缓存条目过多，清理一部分
	totalCacheSize := len(m.companyStatusCache) + len(m.providerStatusCache) + len(m.mappingStatusCache)
	if totalCacheSize > m.maxCacheSize {
		// 清理最老的缓存条目（简单策略：清空一半）
		newCompanyCache := make(map[string]bool)
		count := 0
		for k, v := range m.companyStatusCache {
			if count < len(m.companyStatusCache)/2 {
				newCompanyCache[k] = v
				count++
			}
		}
		m.companyStatusCache = newCompanyCache

		newProviderCache := make(map[string]bool)
		count = 0
		for k, v := range m.providerStatusCache {
			if count < len(m.providerStatusCache)/2 {
				newProviderCache[k] = v
				count++
			}
		}
		m.providerStatusCache = newProviderCache

		m.logger.Info("缓存优化完成，清理了过期条目",
			zap.Int("original_size", totalCacheSize),
			zap.Int("new_size", len(m.companyStatusCache)+len(m.providerStatusCache)+len(m.mappingStatusCache)))
	}
}
