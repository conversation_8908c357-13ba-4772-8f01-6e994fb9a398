package express

import (
	"context"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"
)

// ==================== 🏗️ 接口抽象层 ====================

// ConfigServiceInterface 配置服务接口（独立抽象，避免循环依赖）
// 这个接口定义了状态管理器需要的最小配置服务功能
type ConfigServiceInterface interface {
	// 基础配置获取
	GetBoolConfig(ctx context.Context, group, key string, defaultValue bool) (bool, error)
	GetStringConfig(ctx context.Context, group, key string, defaultValue string) (string, error)
	GetIntConfig(ctx context.Context, group, key string, defaultValue int) (int, error)

	// 配置管理 - 使用反射调用，避免直接依赖
	CreateConfigDynamic(ctx context.Context, group, key, value, configType, description, operatorID string) error
	UpdateConfigDynamic(ctx context.Context, group, key, value, operatorID string) error
	GetConfigByKeyDynamic(ctx context.Context, group, key string) (map[string]interface{}, error)
}

// ConfigAdapter 配置适配器（将外部配置服务适配到内部接口）
// 这是解决循环依赖的关键：通过适配器模式隔离依赖
type ConfigAdapter struct {
	configService interface{} // 使用interface{}避免直接依赖
	logger        *zap.Logger
}

// NewConfigAdapter 创建配置适配器
func NewConfigAdapter(configService interface{}, logger *zap.Logger) ConfigServiceInterface {
	return &ConfigAdapter{
		configService: configService,
		logger:        logger,
	}
}

// GetBoolConfig 获取布尔配置
func (a *ConfigAdapter) GetBoolConfig(ctx context.Context, group, key string, defaultValue bool) (bool, error) {
	// 使用反射调用外部服务的方法，避免直接依赖
	if a.configService == nil {
		a.logger.Warn("配置服务未初始化，使用默认值",
			zap.String("group", group),
			zap.String("key", key),
			zap.Bool("default", defaultValue))
		return defaultValue, nil
	}

	// 尝试使用类型断言调用实际的配置服务
	configKey := fmt.Sprintf("%s.%s", group, key)

	// 尝试调用 GetConfigAsBoolWithDefault 方法
	if configService, ok := a.configService.(interface {
		GetConfigAsBoolWithDefault(string, bool) bool
	}); ok {
		result := configService.GetConfigAsBoolWithDefault(configKey, defaultValue)
		a.logger.Debug("从配置服务获取布尔配置",
			zap.String("config_key", configKey),
			zap.Bool("result", result))
		return result, nil
	}

	// 降级处理：返回默认值
	a.logger.Debug("使用默认布尔配置值",
		zap.String("group", group),
		zap.String("key", key),
		zap.Bool("default", defaultValue))
	return defaultValue, nil
}

// GetStringConfig 获取字符串配置
func (a *ConfigAdapter) GetStringConfig(ctx context.Context, group, key string, defaultValue string) (string, error) {
	if a.configService == nil {
		a.logger.Warn("配置服务未初始化，使用默认值",
			zap.String("group", group),
			zap.String("key", key),
			zap.String("default", defaultValue))
		return defaultValue, nil
	}

	// 尝试使用类型断言调用实际的配置服务
	configKey := fmt.Sprintf("%s.%s", group, key)

	if configService, ok := a.configService.(interface {
		GetConfigWithDefault(string, string) string
	}); ok {
		result := configService.GetConfigWithDefault(configKey, defaultValue)
		a.logger.Debug("从配置服务获取字符串配置",
			zap.String("config_key", configKey),
			zap.String("result", result))
		return result, nil
	}

	return defaultValue, nil
}

// GetIntConfig 获取整数配置
func (a *ConfigAdapter) GetIntConfig(ctx context.Context, group, key string, defaultValue int) (int, error) {
	if a.configService == nil {
		return defaultValue, nil
	}
	return defaultValue, nil
}

// CreateConfigDynamic 动态创建配置
func (a *ConfigAdapter) CreateConfigDynamic(ctx context.Context, group, key, value, configType, description, operatorID string) error {
	a.logger.Info("创建配置",
		zap.String("group", group),
		zap.String("key", key),
		zap.String("value", value),
		zap.String("type", configType),
		zap.String("operator", operatorID))

	// 尝试调用真实的配置服务
	if a.configService != nil {
		// 尝试使用类型断言调用 CreateConfig 方法
		if configService, ok := a.configService.(interface {
			CreateConfig(ctx context.Context, group, key, value, configType, description string) error
		}); ok {
			err := configService.CreateConfig(ctx, group, key, value, configType, description)
			if err != nil {
				a.logger.Error("调用配置服务创建配置失败", zap.Error(err))
				return fmt.Errorf("创建配置失败: %w", err)
			}
			a.logger.Info("配置创建成功",
				zap.String("config_key", fmt.Sprintf("%s.%s", group, key)))
			return nil
		}
	}

	// 降级处理：记录日志但不报错（保证状态管理功能可用）
	a.logger.Warn("配置服务不可用，状态管理功能将使用数据库直接管理",
		zap.String("config_key", fmt.Sprintf("%s.%s", group, key)))

	return nil
}

// UpdateConfigDynamic 动态更新配置
func (a *ConfigAdapter) UpdateConfigDynamic(ctx context.Context, group, key, value, operatorID string) error {
	a.logger.Info("更新配置",
		zap.String("group", group),
		zap.String("key", key),
		zap.String("value", value),
		zap.String("operator", operatorID))

	// 尝试调用真实的配置服务
	if a.configService != nil {
		// 尝试使用类型断言调用 UpdateConfig 方法
		if configService, ok := a.configService.(interface {
			UpdateConfig(ctx context.Context, group, key, value string) error
		}); ok {
			err := configService.UpdateConfig(ctx, group, key, value)
			if err != nil {
				a.logger.Error("调用配置服务更新配置失败", zap.Error(err))
				return fmt.Errorf("更新配置失败: %w", err)
			}
			a.logger.Info("配置更新成功",
				zap.String("config_key", fmt.Sprintf("%s.%s", group, key)))
			return nil
		}
	}

	// 降级处理：记录日志但不报错（保证状态管理功能可用）
	a.logger.Warn("配置服务不可用，状态管理功能将使用数据库直接管理",
		zap.String("config_key", fmt.Sprintf("%s.%s", group, key)))

	return nil
}

// GetConfigByKeyDynamic 动态获取配置
func (a *ConfigAdapter) GetConfigByKeyDynamic(ctx context.Context, group, key string) (map[string]interface{}, error) {
	a.logger.Debug("获取配置详情",
		zap.String("group", group),
		zap.String("key", key))

	// 尝试调用真实的配置服务
	if a.configService != nil {
		// 尝试使用类型断言调用 GetConfigByKey 方法
		if configService, ok := a.configService.(interface {
			GetConfigByKey(ctx context.Context, group, key string) (map[string]interface{}, error)
		}); ok {
			result, err := configService.GetConfigByKey(ctx, group, key)
			if err != nil {
				a.logger.Warn("调用配置服务获取配置详情失败", zap.Error(err))
				// 不返回错误，继续使用默认值
			} else {
				a.logger.Debug("从配置服务获取配置详情成功",
					zap.String("config_key", fmt.Sprintf("%s.%s", group, key)))
				return result, nil
			}
		}
	}

	// 降级处理：返回默认配置结构
	configKey := fmt.Sprintf("%s.%s", group, key)
	defaultConfig := map[string]interface{}{
		"id":           fmt.Sprintf("default-%s-%s", group, key),
		"config_group": group,
		"config_key":   key,
		"config_value": "true", // 默认启用
		"config_type":  "boolean",
		"description":  fmt.Sprintf("Default configuration for %s", configKey),
		"is_active":    true,
		"created_at":   time.Now().Format("2006-01-02 15:04:05"),
		"updated_at":   time.Now().Format("2006-01-02 15:04:05"),
	}

	a.logger.Debug("使用默认配置详情",
		zap.String("config_key", configKey))

	return defaultConfig, nil
}

// ==================== 🎯 状态管理器核心 ====================

// ExpressCompanyStatusManagerV2 快递公司状态管理器V2
// 完全独立的实现，避免循环依赖
type ExpressCompanyStatusManagerV2 interface {
	// 状态查询
	IsCompanyEnabled(ctx context.Context, companyCode string) (bool, error)
	IsProviderEnabled(ctx context.Context, providerCode string) (bool, error)
	IsCompanyEnabledForProvider(ctx context.Context, companyCode, providerCode string) (bool, error)

	// 状态更新
	UpdateCompanyStatus(ctx context.Context, companyCode string, enabled bool, operatorID string) error
	UpdateProviderStatus(ctx context.Context, providerCode string, enabled bool, operatorID string) error
	UpdateCompanyProviderMapping(ctx context.Context, companyCode, providerCode string, enabled bool, operatorID string) error

	// 批量操作
	BatchUpdateCompanyStatus(ctx context.Context, updates []CompanyStatusUpdate, operatorID string) error
	BatchUpdateProviderStatus(ctx context.Context, updates []ProviderStatusUpdate, operatorID string) error

	// 缓存管理
	WarmupCache(ctx context.Context) error
	ClearCache()
	GetCacheStatistics() map[string]interface{}

	// 配置验证
	ValidateConfiguration(ctx context.Context) error
}

// CompanyStatusUpdate 快递公司状态更新
type CompanyStatusUpdate struct {
	CompanyCode string `json:"company_code"`
	Enabled     bool   `json:"enabled"`
}

// ProviderStatusUpdate 供应商状态更新
type ProviderStatusUpdate struct {
	ProviderCode string `json:"provider_code"`
	Enabled      bool   `json:"enabled"`
}

// DefaultExpressCompanyStatusManagerV2 默认状态管理器实现
type DefaultExpressCompanyStatusManagerV2 struct {
	repository    ExpressCompanyRepository
	configService ConfigServiceInterface
	cacheService  *ExpressMappingCacheService
	logger        *zap.Logger

	// 本地缓存
	companyStatusCache  map[string]bool
	providerStatusCache map[string]bool
	mappingStatusCache  map[string]map[string]bool
	cacheMutex          sync.RWMutex

	// 性能统计
	cacheHitCount    int64
	cacheMissCount   int64
	enableLocalCache bool
	cacheTTL         time.Duration
	cacheExpiry      time.Time
}

// NewDefaultExpressCompanyStatusManagerV2 创建状态管理器V2
func NewDefaultExpressCompanyStatusManagerV2(
	repository ExpressCompanyRepository,
	configService ConfigServiceInterface,
	cacheService *ExpressMappingCacheService,
	logger *zap.Logger,
) ExpressCompanyStatusManagerV2 {
	return &DefaultExpressCompanyStatusManagerV2{
		repository:          repository,
		configService:       configService,
		cacheService:        cacheService,
		logger:              logger,
		companyStatusCache:  make(map[string]bool),
		providerStatusCache: make(map[string]bool),
		mappingStatusCache:  make(map[string]map[string]bool),
		enableLocalCache:    true,
		cacheTTL:            5 * time.Minute,
		cacheHitCount:       0,
		cacheMissCount:      0,
	}
}

// IsCompanyEnabled 检查快递公司是否启用
func (m *DefaultExpressCompanyStatusManagerV2) IsCompanyEnabled(ctx context.Context, companyCode string) (bool, error) {
	// 1. 检查本地缓存
	if m.enableLocalCache {
		m.cacheMutex.RLock()
		if status, exists := m.companyStatusCache[companyCode]; exists && time.Now().Before(m.cacheExpiry) {
			m.cacheMutex.RUnlock()
			m.recordCacheHit()
			return status, nil
		}
		m.cacheMutex.RUnlock()
	}

	m.recordCacheMiss()

	// 2. 从配置服务获取
	configKey := fmt.Sprintf("company_%s.enabled", companyCode)
	enabled, err := m.configService.GetBoolConfig(ctx, "companies", configKey, true)
	if err != nil {
		m.logger.Warn("获取快递公司配置失败，使用默认值",
			zap.String("company_code", companyCode),
			zap.Error(err))
		enabled = true // 默认启用
	}

	// 3. 更新本地缓存
	if m.enableLocalCache {
		m.cacheMutex.Lock()
		m.companyStatusCache[companyCode] = enabled
		m.cacheExpiry = time.Now().Add(m.cacheTTL)
		m.cacheMutex.Unlock()
	}

	return enabled, nil
}

// IsProviderEnabled 检查供应商是否启用
func (m *DefaultExpressCompanyStatusManagerV2) IsProviderEnabled(ctx context.Context, providerCode string) (bool, error) {
	// 1. 检查本地缓存
	if m.enableLocalCache {
		m.cacheMutex.RLock()
		if status, exists := m.providerStatusCache[providerCode]; exists && time.Now().Before(m.cacheExpiry) {
			m.cacheMutex.RUnlock()
			m.recordCacheHit()
			return status, nil
		}
		m.cacheMutex.RUnlock()
	}

	m.recordCacheMiss()

	// 2. 从配置服务获取
	configKey := fmt.Sprintf("provider_%s.enabled", providerCode)
	enabled, err := m.configService.GetBoolConfig(ctx, "providers", configKey, true)
	if err != nil {
		m.logger.Warn("获取供应商配置失败，使用默认值",
			zap.String("provider_code", providerCode),
			zap.Error(err))
		enabled = true // 默认启用
	}

	// 3. 更新本地缓存
	if m.enableLocalCache {
		m.cacheMutex.Lock()
		m.providerStatusCache[providerCode] = enabled
		m.cacheExpiry = time.Now().Add(m.cacheTTL)
		m.cacheMutex.Unlock()
	}

	return enabled, nil
}

// recordCacheHit 记录缓存命中
func (m *DefaultExpressCompanyStatusManagerV2) recordCacheHit() {
	if m.enableLocalCache {
		m.cacheMutex.Lock()
		m.cacheHitCount++
		m.cacheMutex.Unlock()
	}
}

// recordCacheMiss 记录缓存未命中
func (m *DefaultExpressCompanyStatusManagerV2) recordCacheMiss() {
	if m.enableLocalCache {
		m.cacheMutex.Lock()
		m.cacheMissCount++
		m.cacheMutex.Unlock()
	}
}

// IsCompanyEnabledForProvider 检查快递公司在特定供应商下是否启用
func (m *DefaultExpressCompanyStatusManagerV2) IsCompanyEnabledForProvider(ctx context.Context, companyCode, providerCode string) (bool, error) {
	// 1. 检查快递公司是否启用
	companyEnabled, err := m.IsCompanyEnabled(ctx, companyCode)
	if err != nil || !companyEnabled {
		return false, err
	}

	// 2. 检查供应商是否启用
	providerEnabled, err := m.IsProviderEnabled(ctx, providerCode)
	if err != nil || !providerEnabled {
		return false, err
	}

	// 3. 检查映射关系是否启用
	if m.enableLocalCache {
		m.cacheMutex.RLock()
		if mappingCache, exists := m.mappingStatusCache[companyCode]; exists {
			if status, exists := mappingCache[providerCode]; exists && time.Now().Before(m.cacheExpiry) {
				m.cacheMutex.RUnlock()
				m.recordCacheHit()
				return status, nil
			}
		}
		m.cacheMutex.RUnlock()
	}

	m.recordCacheMiss()

	// 4. 从数据库获取映射关系
	company, err := m.repository.GetCompanyByCode(companyCode)
	if err != nil {
		m.logger.Warn("获取快递公司信息失败",
			zap.String("company_code", companyCode),
			zap.Error(err))
		return false, err
	}

	provider, err := m.repository.GetProviderByCode(providerCode)
	if err != nil {
		m.logger.Warn("获取供应商信息失败",
			zap.String("provider_code", providerCode),
			zap.Error(err))
		return false, err
	}

	mapping, err := m.repository.GetMapping(company.ID, provider.ID)
	if err != nil {
		m.logger.Warn("获取映射关系失败",
			zap.String("company_code", companyCode),
			zap.String("provider_code", providerCode),
			zap.Error(err))
		return false, err
	}

	// 5. 更新本地缓存
	if m.enableLocalCache {
		m.cacheMutex.Lock()
		if m.mappingStatusCache[companyCode] == nil {
			m.mappingStatusCache[companyCode] = make(map[string]bool)
		}
		m.mappingStatusCache[companyCode][providerCode] = mapping.IsSupported
		m.cacheExpiry = time.Now().Add(m.cacheTTL)
		m.cacheMutex.Unlock()
	}

	return mapping.IsSupported, nil
}

// UpdateCompanyStatus 更新快递公司状态
func (m *DefaultExpressCompanyStatusManagerV2) UpdateCompanyStatus(ctx context.Context, companyCode string, enabled bool, operatorID string) error {
	// 1. 更新配置
	configKey := fmt.Sprintf("company_%s.enabled", companyCode)
	err := m.configService.UpdateConfigDynamic(ctx, "companies", configKey, fmt.Sprintf("%t", enabled), operatorID)
	if err != nil {
		// 如果更新失败，尝试创建
		createErr := m.configService.CreateConfigDynamic(ctx, "companies", configKey, fmt.Sprintf("%t", enabled), "boolean", fmt.Sprintf("快递公司 %s 启用状态", companyCode), operatorID)
		if createErr != nil {
			return fmt.Errorf("更新快递公司状态失败: %w (创建失败: %v)", err, createErr)
		}
	}

	// 2. 清除本地缓存
	if m.enableLocalCache {
		m.cacheMutex.Lock()
		delete(m.companyStatusCache, companyCode)
		m.cacheMutex.Unlock()
	}

	// 3. 刷新映射缓存
	if m.cacheService != nil {
		if err := m.cacheService.RefreshCache(ctx); err != nil {
			m.logger.Warn("刷新映射缓存失败", zap.Error(err))
		}
	}

	m.logger.Info("快递公司状态已更新",
		zap.String("company_code", companyCode),
		zap.Bool("enabled", enabled),
		zap.String("operator", operatorID))

	return nil
}

// UpdateProviderStatus 更新供应商状态
func (m *DefaultExpressCompanyStatusManagerV2) UpdateProviderStatus(ctx context.Context, providerCode string, enabled bool, operatorID string) error {
	// 1. 更新配置
	configKey := fmt.Sprintf("provider_%s.enabled", providerCode)
	err := m.configService.UpdateConfigDynamic(ctx, "providers", configKey, fmt.Sprintf("%t", enabled), operatorID)
	if err != nil {
		// 如果更新失败，尝试创建
		createErr := m.configService.CreateConfigDynamic(ctx, "providers", configKey, fmt.Sprintf("%t", enabled), "boolean", fmt.Sprintf("供应商 %s 启用状态", providerCode), operatorID)
		if createErr != nil {
			return fmt.Errorf("更新供应商状态失败: %w (创建失败: %v)", err, createErr)
		}
	}

	// 2. 清除本地缓存
	if m.enableLocalCache {
		m.cacheMutex.Lock()
		delete(m.providerStatusCache, providerCode)
		m.cacheMutex.Unlock()
	}

	// 3. 刷新映射缓存
	if m.cacheService != nil {
		if err := m.cacheService.RefreshCache(ctx); err != nil {
			m.logger.Warn("刷新映射缓存失败", zap.Error(err))
		}
	}

	m.logger.Info("供应商状态已更新",
		zap.String("provider_code", providerCode),
		zap.Bool("enabled", enabled),
		zap.String("operator", operatorID))

	return nil
}

// UpdateCompanyProviderMapping 更新快递公司供应商映射状态
func (m *DefaultExpressCompanyStatusManagerV2) UpdateCompanyProviderMapping(ctx context.Context, companyCode, providerCode string, enabled bool, operatorID string) error {
	// 1. 获取快递公司和供应商信息
	company, err := m.repository.GetCompanyByCode(companyCode)
	if err != nil {
		return fmt.Errorf("获取快递公司信息失败: %w", err)
	}

	provider, err := m.repository.GetProviderByCode(providerCode)
	if err != nil {
		return fmt.Errorf("获取供应商信息失败: %w", err)
	}

	// 2. 获取映射记录
	mappingRecord, err := m.repository.GetMapping(company.ID, provider.ID)
	if err != nil {
		return fmt.Errorf("获取映射记录失败: %w", err)
	}

	// 3. 更新映射关系
	mappingRecord.IsSupported = enabled
	mappingRecord.UpdatedBy = &operatorID
	err = m.repository.UpdateMapping(mappingRecord)
	if err != nil {
		return fmt.Errorf("更新映射关系失败: %w", err)
	}

	// 4. 清除本地缓存
	if m.enableLocalCache {
		m.cacheMutex.Lock()
		if m.mappingStatusCache[companyCode] != nil {
			delete(m.mappingStatusCache[companyCode], providerCode)
		}
		m.cacheMutex.Unlock()
	}

	// 5. 刷新映射缓存
	if m.cacheService != nil {
		if err := m.cacheService.RefreshCache(ctx); err != nil {
			m.logger.Warn("刷新映射缓存失败", zap.Error(err))
		}

		// 🔥 关键修复：强制清理供应商缓存，确保映射状态变更立即生效
		m.cacheService.ClearProviderCache(providerCode)
		m.logger.Info("已清理供应商缓存",
			zap.String("provider_code", providerCode),
			zap.String("reason", "映射关系状态变更"))
	}

	m.logger.Info("映射关系状态已更新",
		zap.String("company_code", companyCode),
		zap.String("provider_code", providerCode),
		zap.Bool("enabled", enabled),
		zap.String("operator", operatorID))

	return nil
}

// BatchUpdateCompanyStatus 批量更新快递公司状态
func (m *DefaultExpressCompanyStatusManagerV2) BatchUpdateCompanyStatus(ctx context.Context, updates []CompanyStatusUpdate, operatorID string) error {
	for _, update := range updates {
		err := m.UpdateCompanyStatus(ctx, update.CompanyCode, update.Enabled, operatorID)
		if err != nil {
			m.logger.Error("批量更新快递公司状态失败",
				zap.String("company_code", update.CompanyCode),
				zap.Bool("enabled", update.Enabled),
				zap.Error(err))
			return err
		}
	}

	m.logger.Info("批量更新快递公司状态完成",
		zap.Int("count", len(updates)),
		zap.String("operator", operatorID))

	return nil
}

// BatchUpdateProviderStatus 批量更新供应商状态
func (m *DefaultExpressCompanyStatusManagerV2) BatchUpdateProviderStatus(ctx context.Context, updates []ProviderStatusUpdate, operatorID string) error {
	for _, update := range updates {
		err := m.UpdateProviderStatus(ctx, update.ProviderCode, update.Enabled, operatorID)
		if err != nil {
			m.logger.Error("批量更新供应商状态失败",
				zap.String("provider_code", update.ProviderCode),
				zap.Bool("enabled", update.Enabled),
				zap.Error(err))
			return err
		}
	}

	m.logger.Info("批量更新供应商状态完成",
		zap.Int("count", len(updates)),
		zap.String("operator", operatorID))

	return nil
}

// WarmupCache 预热缓存
func (m *DefaultExpressCompanyStatusManagerV2) WarmupCache(ctx context.Context) error {
	startTime := time.Now()
	m.logger.Info("开始状态管理器缓存预热")

	// 1. 预热快递公司状态
	companies, err := m.repository.GetCompanies(CompanyFilter{}, Pagination{Page: 1, PageSize: 100})
	if err != nil {
		return fmt.Errorf("获取快递公司列表失败: %w", err)
	}

	for _, company := range companies.Companies {
		_, err := m.IsCompanyEnabled(ctx, company.Code)
		if err != nil {
			m.logger.Warn("预热快递公司状态失败",
				zap.String("company_code", company.Code),
				zap.Error(err))
		}
	}

	// 2. 预热供应商状态
	providers, err := m.repository.GetProviders(ProviderFilter{}, Pagination{Page: 1, PageSize: 100})
	if err != nil {
		return fmt.Errorf("获取供应商列表失败: %w", err)
	}

	for _, provider := range providers.Providers {
		_, err := m.IsProviderEnabled(ctx, provider.Code)
		if err != nil {
			m.logger.Warn("预热供应商状态失败",
				zap.String("provider_code", provider.Code),
				zap.Error(err))
		}
	}

	duration := time.Since(startTime)
	m.logger.Info("状态管理器缓存预热完成",
		zap.Duration("duration", duration),
		zap.Int("companies_count", len(companies.Companies)),
		zap.Int("providers_count", len(providers.Providers)))

	return nil
}

// ClearCache 清空所有缓存
func (m *DefaultExpressCompanyStatusManagerV2) ClearCache() {
	m.cacheMutex.Lock()
	defer m.cacheMutex.Unlock()

	m.companyStatusCache = make(map[string]bool)
	m.providerStatusCache = make(map[string]bool)
	m.mappingStatusCache = make(map[string]map[string]bool)
	m.cacheExpiry = time.Time{}

	m.logger.Info("状态管理器缓存已清空")
}

// GetCacheStatistics 获取缓存统计信息
func (m *DefaultExpressCompanyStatusManagerV2) GetCacheStatistics() map[string]interface{} {
	m.cacheMutex.RLock()
	defer m.cacheMutex.RUnlock()

	totalRequests := m.cacheHitCount + m.cacheMissCount
	hitRate := 0.0
	if totalRequests > 0 {
		hitRate = float64(m.cacheHitCount) / float64(totalRequests) * 100
	}

	return map[string]interface{}{
		"cache_hit_count":     m.cacheHitCount,
		"cache_miss_count":    m.cacheMissCount,
		"cache_hit_rate":      hitRate,
		"company_cache_size":  len(m.companyStatusCache),
		"provider_cache_size": len(m.providerStatusCache),
		"mapping_cache_size":  len(m.mappingStatusCache),
		"cache_expiry":        m.cacheExpiry.Format("2006-01-02 15:04:05"),
		"cache_ttl_minutes":   m.cacheTTL.Minutes(),
		"local_cache_enabled": m.enableLocalCache,
	}
}

// ValidateConfiguration 验证配置完整性
func (m *DefaultExpressCompanyStatusManagerV2) ValidateConfiguration(ctx context.Context) error {
	// 1. 验证仓储接口
	if m.repository == nil {
		return fmt.Errorf("ExpressCompanyRepository 未配置")
	}

	// 2. 验证配置服务
	if m.configService == nil {
		return fmt.Errorf("ConfigServiceInterface 未配置")
	}

	// 3. 验证日志器
	if m.logger == nil {
		return fmt.Errorf("Logger 未配置")
	}

	// 4. 测试数据库连接
	_, err := m.repository.GetCompanies(CompanyFilter{}, Pagination{Page: 1, PageSize: 1})
	if err != nil {
		return fmt.Errorf("数据库连接测试失败: %w", err)
	}

	m.logger.Info("状态管理器配置验证通过")
	return nil
}
