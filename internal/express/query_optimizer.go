package express

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"go.uber.org/zap"
)

// QueryOptimizer 数据库查询优化器
// 提供查询性能分析、索引建议和查询优化功能
type QueryOptimizer interface {
	// AnalyzeQueryPerformance 分析查询性能
	AnalyzeQueryPerformance(ctx context.Context, query string, args ...interface{}) (*QueryPerformanceReport, error)

	// SuggestIndexes 建议索引
	SuggestIndexes(ctx context.Context, tableName string) ([]*IndexSuggestion, error)

	// OptimizeExpressQueries 优化快递相关查询
	OptimizeExpressQueries(ctx context.Context) (*OptimizationReport, error)

	// GetSlowQueries 获取慢查询列表
	GetSlowQueries(ctx context.Context, threshold time.Duration) ([]*SlowQuery, error)

	// CreateOptimalIndexes 创建最优索引
	CreateOptimalIndexes(ctx context.Context) error
}

// QueryPerformanceReport 查询性能报告
type QueryPerformanceReport struct {
	Query          string        `json:"query"`           // 查询语句
	ExecutionTime  time.Duration `json:"execution_time"`  // 执行时间
	RowsExamined   int64         `json:"rows_examined"`   // 扫描行数
	RowsReturned   int64         `json:"rows_returned"`   // 返回行数
	IndexUsed      bool          `json:"index_used"`      // 是否使用索引
	IndexName      string        `json:"index_name"`      // 使用的索引名
	Suggestions    []string      `json:"suggestions"`     // 优化建议
	OptimizedQuery string        `json:"optimized_query"` // 优化后的查询
}

// IndexSuggestion 索引建议
type IndexSuggestion struct {
	TableName string   `json:"table_name"` // 表名
	IndexName string   `json:"index_name"` // 建议的索引名
	Columns   []string `json:"columns"`    // 索引列
	IndexType string   `json:"index_type"` // 索引类型
	Reason    string   `json:"reason"`     // 建议原因
	Priority  int      `json:"priority"`   // 优先级（1-10）
	CreateSQL string   `json:"create_sql"` // 创建SQL
}

// OptimizationReport 优化报告
type OptimizationReport struct {
	TotalQueries       int                `json:"total_queries"`       // 总查询数
	OptimizedQueries   int                `json:"optimized_queries"`   // 已优化查询数
	IndexSuggestions   []*IndexSuggestion `json:"index_suggestions"`   // 索引建议
	SlowQueries        []*SlowQuery       `json:"slow_queries"`        // 慢查询
	PerformanceGain    float64            `json:"performance_gain"`    // 性能提升百分比
	RecommendedActions []string           `json:"recommended_actions"` // 推荐操作
}

// SlowQuery 慢查询
type SlowQuery struct {
	Query         string        `json:"query"`          // 查询语句
	ExecutionTime time.Duration `json:"execution_time"` // 执行时间
	CallCount     int           `json:"call_count"`     // 调用次数
	LastSeen      time.Time     `json:"last_seen"`      // 最后出现时间
	Impact        string        `json:"impact"`         // 影响程度
}

// DefaultQueryOptimizer 默认查询优化器实现
type DefaultQueryOptimizer struct {
	db     *sql.DB
	logger *zap.Logger

	// 优化配置
	slowQueryThreshold time.Duration
	maxAnalysisQueries int
}

// NewDefaultQueryOptimizer 创建默认查询优化器
func NewDefaultQueryOptimizer(db *sql.DB, logger *zap.Logger) QueryOptimizer {
	return &DefaultQueryOptimizer{
		db:                 db,
		logger:             logger,
		slowQueryThreshold: 100 * time.Millisecond, // 100ms为慢查询阈值
		maxAnalysisQueries: 100,                    // 最多分析100个查询
	}
}

// AnalyzeQueryPerformance 分析查询性能
func (o *DefaultQueryOptimizer) AnalyzeQueryPerformance(ctx context.Context, query string, args ...interface{}) (*QueryPerformanceReport, error) {
	report := &QueryPerformanceReport{
		Query:       query,
		Suggestions: make([]string, 0),
	}

	// 1. 执行EXPLAIN分析
	explainQuery := "EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) " + query
	startTime := time.Now()

	rows, err := o.db.QueryContext(ctx, explainQuery, args...)
	if err != nil {
		return nil, fmt.Errorf("执行EXPLAIN失败: %w", err)
	}
	defer rows.Close()

	report.ExecutionTime = time.Since(startTime)

	// 2. 解析EXPLAIN结果
	if rows.Next() {
		var explainResult string
		if err := rows.Scan(&explainResult); err != nil {
			return nil, fmt.Errorf("读取EXPLAIN结果失败: %w", err)
		}

		// 解析JSON结果（简化版本）
		o.parseExplainResult(explainResult, report)
	}

	// 3. 生成优化建议
	o.generateOptimizationSuggestions(report)

	o.logger.Debug("查询性能分析完成",
		zap.String("query", query),
		zap.Duration("execution_time", report.ExecutionTime),
		zap.Bool("index_used", report.IndexUsed))

	return report, nil
}

// SuggestIndexes 建议索引
func (o *DefaultQueryOptimizer) SuggestIndexes(ctx context.Context, tableName string) ([]*IndexSuggestion, error) {
	suggestions := make([]*IndexSuggestion, 0)

	// 快递公司表的索引建议
	if tableName == "express_companies" || tableName == "" {
		suggestions = append(suggestions, []*IndexSuggestion{
			{
				TableName: "express_companies",
				IndexName: "idx_express_companies_code_active",
				Columns:   []string{"code", "is_active"},
				IndexType: "BTREE",
				Reason:    "优化按代码和状态查询的性能",
				Priority:  9,
				CreateSQL: "CREATE INDEX CONCURRENTLY idx_express_companies_code_active ON express_companies (code, is_active);",
			},
			{
				TableName: "express_companies",
				IndexName: "idx_express_companies_active_name",
				Columns:   []string{"is_active", "name"},
				IndexType: "BTREE",
				Reason:    "优化获取启用快递公司列表的性能",
				Priority:  8,
				CreateSQL: "CREATE INDEX CONCURRENTLY idx_express_companies_active_name ON express_companies (is_active, name);",
			},
			{
				TableName: "express_companies",
				IndexName: "idx_express_companies_updated_at",
				Columns:   []string{"updated_at"},
				IndexType: "BTREE",
				Reason:    "优化按更新时间排序的查询",
				Priority:  6,
				CreateSQL: "CREATE INDEX CONCURRENTLY idx_express_companies_updated_at ON express_companies (updated_at DESC);",
			},
		}...)
	}

	// 供应商表的索引建议
	if tableName == "express_providers" || tableName == "" {
		suggestions = append(suggestions, []*IndexSuggestion{
			{
				TableName: "express_providers",
				IndexName: "idx_express_providers_code",
				Columns:   []string{"code"},
				IndexType: "BTREE",
				Reason:    "优化按供应商代码查询的性能",
				Priority:  9,
				CreateSQL: "CREATE UNIQUE INDEX CONCURRENTLY idx_express_providers_code ON express_providers (code);",
			},
			{
				TableName: "express_providers",
				IndexName: "idx_express_providers_priority",
				Columns:   []string{"priority"},
				IndexType: "BTREE",
				Reason:    "优化按优先级排序的查询",
				Priority:  7,
				CreateSQL: "CREATE INDEX CONCURRENTLY idx_express_providers_priority ON express_providers (priority);",
			},
		}...)
	}

	// 映射关系表的索引建议
	if tableName == "express_company_provider_mappings" || tableName == "" {
		suggestions = append(suggestions, []*IndexSuggestion{
			{
				TableName: "express_company_provider_mappings",
				IndexName: "idx_mappings_company_provider_supported",
				Columns:   []string{"company_id", "provider_id", "is_supported"},
				IndexType: "BTREE",
				Reason:    "优化映射关系查询的性能",
				Priority:  10,
				CreateSQL: "CREATE INDEX CONCURRENTLY idx_mappings_company_provider_supported ON express_company_provider_mappings (company_id, provider_id, is_supported);",
			},
			{
				TableName: "express_company_provider_mappings",
				IndexName: "idx_mappings_provider_supported",
				Columns:   []string{"provider_id", "is_supported"},
				IndexType: "BTREE",
				Reason:    "优化按供应商获取支持的快递公司",
				Priority:  8,
				CreateSQL: "CREATE INDEX CONCURRENTLY idx_mappings_provider_supported ON express_company_provider_mappings (provider_id, is_supported);",
			},
		}...)
	}

	// 系统配置表的索引建议
	if tableName == "system_configs" || tableName == "" {
		suggestions = append(suggestions, []*IndexSuggestion{
			{
				TableName: "system_configs",
				IndexName: "idx_system_configs_group_key",
				Columns:   []string{"config_group", "config_key"},
				IndexType: "BTREE",
				Reason:    "优化配置查询的性能",
				Priority:  9,
				CreateSQL: "CREATE UNIQUE INDEX CONCURRENTLY idx_system_configs_group_key ON system_configs (config_group, config_key);",
			},
		}...)
	}

	o.logger.Info("索引建议生成完成",
		zap.String("table_name", tableName),
		zap.Int("suggestions_count", len(suggestions)))

	return suggestions, nil
}

// OptimizeExpressQueries 优化快递相关查询
func (o *DefaultQueryOptimizer) OptimizeExpressQueries(ctx context.Context) (*OptimizationReport, error) {
	report := &OptimizationReport{
		IndexSuggestions:   make([]*IndexSuggestion, 0),
		SlowQueries:        make([]*SlowQuery, 0),
		RecommendedActions: make([]string, 0),
	}

	// 1. 获取索引建议
	suggestions, err := o.SuggestIndexes(ctx, "")
	if err != nil {
		return nil, fmt.Errorf("获取索引建议失败: %w", err)
	}
	report.IndexSuggestions = suggestions

	// 2. 获取慢查询
	slowQueries, err := o.GetSlowQueries(ctx, o.slowQueryThreshold)
	if err != nil {
		o.logger.Warn("获取慢查询失败", zap.Error(err))
	} else {
		report.SlowQueries = slowQueries
	}

	// 3. 生成推荐操作
	report.RecommendedActions = o.generateRecommendedActions(report)

	// 4. 计算性能提升预估
	report.PerformanceGain = o.estimatePerformanceGain(report)

	o.logger.Info("快递查询优化分析完成",
		zap.Int("index_suggestions", len(report.IndexSuggestions)),
		zap.Int("slow_queries", len(report.SlowQueries)),
		zap.Float64("estimated_gain", report.PerformanceGain))

	return report, nil
}

// GetSlowQueries 获取慢查询列表
func (o *DefaultQueryOptimizer) GetSlowQueries(ctx context.Context, threshold time.Duration) ([]*SlowQuery, error) {
	// PostgreSQL慢查询检测（需要启用pg_stat_statements扩展）
	query := `
		SELECT query, mean_exec_time, calls, last_exec
		FROM pg_stat_statements 
		WHERE mean_exec_time > $1 
		AND query LIKE '%express_%'
		ORDER BY mean_exec_time DESC 
		LIMIT $2
	`

	rows, err := o.db.QueryContext(ctx, query, threshold.Milliseconds(), o.maxAnalysisQueries)
	if err != nil {
		// 如果pg_stat_statements不可用，返回空列表
		o.logger.Warn("无法获取慢查询统计信息，可能需要启用pg_stat_statements扩展", zap.Error(err))
		return []*SlowQuery{}, nil
	}
	defer rows.Close()

	slowQueries := make([]*SlowQuery, 0)
	for rows.Next() {
		var query string
		var meanExecTime float64
		var calls int
		var lastExec time.Time

		if err := rows.Scan(&query, &meanExecTime, &calls, &lastExec); err != nil {
			o.logger.Warn("读取慢查询记录失败", zap.Error(err))
			continue
		}

		slowQuery := &SlowQuery{
			Query:         query,
			ExecutionTime: time.Duration(meanExecTime) * time.Millisecond,
			CallCount:     calls,
			LastSeen:      lastExec,
			Impact:        o.calculateQueryImpact(meanExecTime, calls),
		}

		slowQueries = append(slowQueries, slowQuery)
	}

	return slowQueries, nil
}

// CreateOptimalIndexes 创建最优索引
func (o *DefaultQueryOptimizer) CreateOptimalIndexes(ctx context.Context) error {
	suggestions, err := o.SuggestIndexes(ctx, "")
	if err != nil {
		return fmt.Errorf("获取索引建议失败: %w", err)
	}

	// 按优先级排序，优先创建高优先级索引
	highPrioritySuggestions := make([]*IndexSuggestion, 0)
	for _, suggestion := range suggestions {
		if suggestion.Priority >= 8 { // 只创建高优先级索引
			highPrioritySuggestions = append(highPrioritySuggestions, suggestion)
		}
	}

	for _, suggestion := range highPrioritySuggestions {
		// 检查索引是否已存在
		exists, err := o.indexExists(ctx, suggestion.IndexName)
		if err != nil {
			o.logger.Warn("检查索引是否存在失败",
				zap.String("index_name", suggestion.IndexName),
				zap.Error(err))
			continue
		}

		if exists {
			o.logger.Info("索引已存在，跳过创建",
				zap.String("index_name", suggestion.IndexName))
			continue
		}

		// 创建索引
		o.logger.Info("开始创建索引",
			zap.String("index_name", suggestion.IndexName),
			zap.String("table_name", suggestion.TableName))

		_, err = o.db.ExecContext(ctx, suggestion.CreateSQL)
		if err != nil {
			o.logger.Error("创建索引失败",
				zap.String("index_name", suggestion.IndexName),
				zap.String("sql", suggestion.CreateSQL),
				zap.Error(err))
			continue
		}

		o.logger.Info("索引创建成功",
			zap.String("index_name", suggestion.IndexName))
	}

	return nil
}

// ==================== 私有辅助方法 ====================

// parseExplainResult 解析EXPLAIN结果
func (o *DefaultQueryOptimizer) parseExplainResult(explainResult string, report *QueryPerformanceReport) {
	// 解析PostgreSQL EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) 的JSON结果
	var explainData []map[string]interface{}
	if err := json.Unmarshal([]byte(explainResult), &explainData); err != nil {
		o.logger.Warn("解析EXPLAIN结果失败", zap.Error(err))
		// 降级到简单的字符串解析
		o.parseExplainResultFallback(explainResult, report)
		return
	}

	if len(explainData) == 0 {
		return
	}

	// 获取执行计划的根节点
	plan, ok := explainData[0]["Plan"].(map[string]interface{})
	if !ok {
		return
	}

	// 解析执行时间
	if executionTime, exists := explainData[0]["Execution Time"]; exists {
		if execTime, ok := executionTime.(float64); ok {
			report.ExecutionTime = time.Duration(execTime) * time.Millisecond
		}
	}

	// 递归解析执行计划节点
	o.parsePlanNode(plan, report)
}

// parseExplainResultFallback 降级的EXPLAIN结果解析
func (o *DefaultQueryOptimizer) parseExplainResultFallback(explainResult string, report *QueryPerformanceReport) {
	if strings.Contains(explainResult, "Index Scan") || strings.Contains(explainResult, "Index Only Scan") {
		report.IndexUsed = true
		// 尝试提取索引名
		if idx := strings.Index(explainResult, "using "); idx != -1 {
			remaining := explainResult[idx+6:]
			if spaceIdx := strings.Index(remaining, " "); spaceIdx != -1 {
				report.IndexName = remaining[:spaceIdx]
			}
		}
	} else if strings.Contains(explainResult, "Seq Scan") {
		report.IndexUsed = false
		report.Suggestions = append(report.Suggestions, "检测到全表扫描，建议添加索引优化查询性能")
	}

	// 尝试提取行数信息
	if strings.Contains(explainResult, "rows=") {
		// 使用正则表达式提取行数
		rowsPattern := `rows=(\d+)`
		if matches := regexp.MustCompile(rowsPattern).FindStringSubmatch(explainResult); len(matches) > 1 {
			if rows, err := strconv.ParseInt(matches[1], 10, 64); err == nil {
				report.RowsExamined = rows
			}
		}
	}
}

// parsePlanNode 递归解析执行计划节点
func (o *DefaultQueryOptimizer) parsePlanNode(node map[string]interface{}, report *QueryPerformanceReport) {
	// 解析节点类型
	nodeType, _ := node["Node Type"].(string)

	// 解析实际行数
	if actualRows, exists := node["Actual Rows"]; exists {
		if rows, ok := actualRows.(float64); ok {
			report.RowsReturned += int64(rows)
		}
	}

	// 解析扫描行数
	if planRows, exists := node["Plan Rows"]; exists {
		if rows, ok := planRows.(float64); ok {
			report.RowsExamined += int64(rows)
		}
	}

	// 检查索引使用情况
	switch nodeType {
	case "Index Scan", "Index Only Scan", "Bitmap Index Scan":
		report.IndexUsed = true
		if indexName, exists := node["Index Name"]; exists {
			if name, ok := indexName.(string); ok {
				report.IndexName = name
			}
		}
	case "Seq Scan":
		if !report.IndexUsed { // 只有在没有使用索引时才标记
			report.IndexUsed = false
			if relationName, exists := node["Relation Name"]; exists {
				if tableName, ok := relationName.(string); ok {
					report.Suggestions = append(report.Suggestions,
						fmt.Sprintf("表 %s 使用了全表扫描，建议添加索引", tableName))
				}
			}
		}
	case "Nested Loop", "Hash Join", "Merge Join":
		// 连接操作，检查是否高效
		if actualRows, exists := node["Actual Rows"]; exists {
			if planRows, exists2 := node["Plan Rows"]; exists2 {
				if actual, ok1 := actualRows.(float64); ok1 {
					if plan, ok2 := planRows.(float64); ok2 {
						// 如果实际行数远大于预估行数，可能需要更新统计信息
						if actual > plan*10 {
							report.Suggestions = append(report.Suggestions,
								"查询计划预估不准确，建议更新表统计信息 (ANALYZE)")
						}
					}
				}
			}
		}
	}

	// 递归处理子节点
	if plans, exists := node["Plans"]; exists {
		if planList, ok := plans.([]interface{}); ok {
			for _, subPlan := range planList {
				if subNode, ok := subPlan.(map[string]interface{}); ok {
					o.parsePlanNode(subNode, report)
				}
			}
		}
	}
}

// generateOptimizationSuggestions 生成优化建议
func (o *DefaultQueryOptimizer) generateOptimizationSuggestions(report *QueryPerformanceReport) {
	query := strings.ToLower(report.Query)

	// 检查是否使用了索引
	if !report.IndexUsed {
		if strings.Contains(query, "where") {
			report.Suggestions = append(report.Suggestions, "WHERE条件未使用索引，建议添加相应索引")
		}
		if strings.Contains(query, "order by") {
			report.Suggestions = append(report.Suggestions, "ORDER BY字段未使用索引，建议添加排序索引")
		}
	}

	// 检查查询模式
	if strings.Contains(query, "select *") {
		report.Suggestions = append(report.Suggestions, "避免使用SELECT *，只查询需要的字段")
	}

	if strings.Contains(query, "like '%") {
		report.Suggestions = append(report.Suggestions, "避免使用前缀通配符LIKE '%xxx'，考虑使用全文搜索")
	}

	// 检查执行时间
	if report.ExecutionTime > 100*time.Millisecond {
		report.Suggestions = append(report.Suggestions, "查询执行时间较长，建议优化查询条件或添加索引")
	}

	// 生成优化后的查询（简化版本）
	report.OptimizedQuery = o.generateOptimizedQuery(report.Query)
}

// generateOptimizedQuery 生成优化后的查询
func (o *DefaultQueryOptimizer) generateOptimizedQuery(originalQuery string) string {
	optimized := originalQuery

	// 简单的优化规则
	if strings.Contains(strings.ToLower(optimized), "select *") {
		// 建议具体字段列表
		optimized = strings.ReplaceAll(optimized, "SELECT *", "SELECT id, code, name, is_active")
	}

	return optimized
}

// generateRecommendedActions 生成推荐操作
func (o *DefaultQueryOptimizer) generateRecommendedActions(report *OptimizationReport) []string {
	actions := make([]string, 0)

	// 基于索引建议生成操作
	highPriorityIndexes := 0
	for _, suggestion := range report.IndexSuggestions {
		if suggestion.Priority >= 8 {
			highPriorityIndexes++
		}
	}

	if highPriorityIndexes > 0 {
		actions = append(actions, fmt.Sprintf("创建 %d 个高优先级索引以提升查询性能", highPriorityIndexes))
	}

	// 基于慢查询生成操作
	if len(report.SlowQueries) > 0 {
		actions = append(actions, fmt.Sprintf("优化 %d 个慢查询", len(report.SlowQueries)))
	}

	// 通用建议
	actions = append(actions, "启用查询缓存以减少数据库负载")
	actions = append(actions, "定期更新表统计信息以优化查询计划")
	actions = append(actions, "监控数据库性能指标")

	return actions
}

// estimatePerformanceGain 估算性能提升
func (o *DefaultQueryOptimizer) estimatePerformanceGain(report *OptimizationReport) float64 {
	gain := 0.0

	// 基于索引建议估算提升
	for _, suggestion := range report.IndexSuggestions {
		switch suggestion.Priority {
		case 10:
			gain += 30.0 // 高优先级索引预计提升30%
		case 9:
			gain += 20.0
		case 8:
			gain += 15.0
		default:
			gain += 5.0
		}
	}

	// 基于慢查询数量估算
	if len(report.SlowQueries) > 0 {
		gain += float64(len(report.SlowQueries)) * 2.0
	}

	// 限制最大提升百分比
	if gain > 80.0 {
		gain = 80.0
	}

	return gain
}

// calculateQueryImpact 计算查询影响程度
func (o *DefaultQueryOptimizer) calculateQueryImpact(meanExecTime float64, calls int) string {
	totalTime := meanExecTime * float64(calls)

	if totalTime > 10000 { // 总时间超过10秒
		return "高"
	} else if totalTime > 1000 { // 总时间超过1秒
		return "中"
	} else {
		return "低"
	}
}

// indexExists 检查索引是否存在
func (o *DefaultQueryOptimizer) indexExists(ctx context.Context, indexName string) (bool, error) {
	query := `
		SELECT EXISTS (
			SELECT 1 FROM pg_indexes
			WHERE indexname = $1
		)
	`

	var exists bool
	err := o.db.QueryRowContext(ctx, query, indexName).Scan(&exists)
	if err != nil {
		return false, fmt.Errorf("检查索引是否存在失败: %w", err)
	}

	return exists, nil
}
