package adapter

import (
	"context"
	"crypto/md5"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"math"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/go-playground/validator/v10"
	"go.uber.org/zap"
	"golang.org/x/time/rate"

	"github.com/your-org/go-kuaidi/internal/config"
	"github.com/your-org/go-kuaidi/internal/express"
	"github.com/your-org/go-kuaidi/internal/model"
	"github.com/your-org/go-kuaidi/internal/util"
)

// formatPhoneForKuaidiNiaoComprehensive 综合处理Mobile和Tel字段，符合快递鸟API要求
// 快递鸟API要求：Mobile（11位手机号码，1开头11位数字）和Tel（区号-尾数）二选一必填
func (a *KuaidiNiaoAdapter) formatPhoneForKuaidiNiaoComprehensive(mobile string, tel string) (resultTel string, resultMobile string) {
	// 优先使用明确指定的tel字段（固定电话）
	if tel != "" {
		a.logger.Info("🔥 快递鸟电话格式化：使用专用Tel字段",
			zap.String("tel", tel))
		return tel, ""
	}

	// 优先使用明确指定的mobile字段（手机号）
	if mobile != "" {
		// 对mobile字段进行智能识别
		telResult, mobileResult := a.formatPhoneForKuaidiNiao(mobile)
		if mobileResult != "" {
			a.logger.Info("🔥 快递鸟电话格式化：Mobile字段识别为手机号",
				zap.String("mobile", mobile),
				zap.String("result", mobileResult))
			return "", mobileResult
		} else {
			a.logger.Info("🔥 快递鸟电话格式化：Mobile字段识别为固定电话",
				zap.String("mobile", mobile),
				zap.String("result", telResult))
			return telResult, ""
		}
	}

	// 都为空的情况
	return "", ""
}

// formatPhoneForKuaidiNiao 智能格式化电话号码，符合快递鸟API要求（使用生产级验证器）
// 快递鸟API要求：Mobile（11位手机号码，1开头11位数字）和Tel（区号-尾数）二选一必填
func (a *KuaidiNiaoAdapter) formatPhoneForKuaidiNiao(phone string) (tel string, mobile string) {
	if phone == "" {
		return "", ""
	}

	// 使用生产级电话验证器
	validator := util.GetPhoneValidator()
	result := validator.ValidatePhone(phone)

	if !result.IsValid {
		a.logger.Warn("快递鸟电话格式化：电话号码格式无效",
			zap.String("original", phone),
			zap.String("error_code", result.ErrorCode))
		return phone, "" // 格式无效时作为固定电话处理
	}

	// 根据验证结果进行格式化
	switch result.Type {
	case util.PhoneTypeMobile:
		a.logger.Debug("快递鸟电话格式化：识别为手机号",
			zap.String("original", phone),
			zap.String("cleaned", result.Cleaned))
		return "", result.Cleaned
	case util.PhoneTypeLandline:
		a.logger.Debug("快递鸟电话格式化：识别为固定电话",
			zap.String("original", phone),
			zap.String("cleaned", result.Cleaned))
		return result.Cleaned, ""
	default:
		a.logger.Warn("快递鸟电话格式化：未知电话类型",
			zap.String("original", phone))
		return phone, ""
	}
}

// NewKuaidiNiaoAdapter 创建快递鸟适配器
func NewKuaidiNiaoAdapter(config KuaidiNiaoConfig, expressCompanyRepo interface{}) *KuaidiNiaoAdapter {
	logger, _ := zap.NewProduction()

	// 创建优化的HTTP客户端
	client := createOptimizedHTTPClient(config.Timeout)

	// 创建限流器 (每秒30个请求，突发60个)
	rateLimiter := rate.NewLimiter(30, 60)

	return &KuaidiNiaoAdapter{
		config:             config,
		client:             client,
		logger:             logger,
		rateLimiter:        rateLimiter,
		validator:          validator.New(),
		expressCompanyRepo: expressCompanyRepo,
		// 初始化缓存相关字段
		priceCache:    make(map[string]*KuaidiNiaoCachedPrices),
		cacheExpiry:   5 * time.Minute, // 缓存5分钟
		lastCacheTime: make(map[string]time.Time),
	}
}

// createOptimizedHTTPClient 创建优化的HTTP客户端
func createOptimizedHTTPClient(timeout int) *http.Client {
	transport := &http.Transport{
		MaxIdleConns:          200,
		MaxIdleConnsPerHost:   50,
		IdleConnTimeout:       60 * time.Second,
		DisableCompression:    false,
		ForceAttemptHTTP2:     true,
		ResponseHeaderTimeout: 8 * time.Second,
		ExpectContinueTimeout: 1 * time.Second,
	}

	return &http.Client{
		Transport: transport,
		Timeout:   time.Duration(timeout) * time.Second,
	}
}

// Name 返回供应商名称
func (a *KuaidiNiaoAdapter) Name() string {
	return "kuaidiniao"
}

// SetMappingService 设置快递映射服务
func (a *KuaidiNiaoAdapter) SetMappingService(service interface{}) {
	a.mappingService = service
}

// QueryPrice 查询价格 - 实现ProviderAdapter接口
func (a *KuaidiNiaoAdapter) QueryPrice(ctx context.Context, req *model.PriceRequest) ([]model.StandardizedPrice, error) {
	start := time.Now()

	// 🔍 详细调试日志：记录输入参数
	a.logger.Info("🚀 快递鸟价格查询开始 - 详细参数",
		zap.String("provider", "kuaidiniao"),
		zap.String("express_type", req.ExpressType),
		zap.String("sender_province", req.Sender.Province),
		zap.String("sender_city", req.Sender.City),
		zap.String("sender_district", req.Sender.District),
		zap.String("sender_address", req.Sender.Address),
		zap.String("receiver_province", req.Receiver.Province),
		zap.String("receiver_city", req.Receiver.City),
		zap.String("receiver_district", req.Receiver.District),
		zap.String("receiver_address", req.Receiver.Address),
		zap.Float64("weight", req.Package.Weight),
		zap.Float64("length", req.Package.Length),
		zap.Float64("width", req.Package.Width),
		zap.Float64("height", req.Package.Height),
		zap.String("goods_name", req.Package.GoodsName),
		zap.String("customer_order_no", req.CustomerOrderNo))

	defer func() {
		duration := time.Since(start)
		a.logger.Info("🏁 快递鸟价格查询完成",
			zap.String("provider", "kuaidiniao"),
			zap.Duration("duration", duration))
	}()

	// 1. 参数验证
	if err := a.validatePriceRequest(req); err != nil {
		a.logger.Error("❌ 快递鸟参数验证失败", zap.Error(err))
		return nil, fmt.Errorf("参数验证失败: %w", err)
	}
	a.logger.Info("✅ 快递鸟参数验证通过")

	// 🔥 修复：如果指定了快递公司，先验证是否支持和启用
	if req.ExpressType != "" {
		// 🚀 新增：检查快递公司启用状态
		if a.mappingService != nil {
			// 类型断言为ExpressMappingService接口
			if mappingService, ok := a.mappingService.(interface {
				GetProviderCompanyCode(ctx context.Context, companyCode, providerCode string) (string, error)
			}); ok {
				mappedCode, err := mappingService.GetProviderCompanyCode(ctx, req.ExpressType, "kuaidiniao")
				if err != nil {
					a.logger.Info("快递鸟不支持该快递公司或快递公司已禁用，跳过查询",
						zap.String("express_type", req.ExpressType),
						zap.Error(err))
					return []model.StandardizedPrice{}, nil
				}
				// 使用数据库映射到的供应商代码作为查价过滤依据
				req.ExpressType = mappedCode
			} else {
				a.logger.Warn("⚠️ 映射服务类型断言失败，跳过状态检查")
			}
		}
	}

	// 2. 生成路线缓存键
	routeKey := a.generateRouteKey(req)
	requestHash := a.generateRequestHash(req)

	a.logger.Info("🔑 生成缓存键",
		zap.String("route_key", routeKey),
		zap.String("request_hash", requestHash))

	// 3. 尝试从缓存获取数据
	if cachedPrices := a.getCachedPrices(routeKey, requestHash); cachedPrices != nil {
		a.logger.Info("✅ 缓存命中，返回缓存数据",
			zap.String("route_key", routeKey),
			zap.Int("cached_prices_count", len(cachedPrices.Prices)))

		// 从缓存中过滤出请求的快递公司价格
		return a.filterPricesByExpressType(cachedPrices.Prices, req.ExpressType)
	}

	a.logger.Info("⚠️ 缓存未命中，准备调用快递鸟API")

	// 4. 限流控制
	if err := a.rateLimiter.Wait(ctx); err != nil {
		a.logger.Error("❌ 快递鸟限流等待失败", zap.Error(err))
		return nil, fmt.Errorf("限流等待失败: %w", err)
	}
	a.logger.Debug("✅ 快递鸟限流控制通过")

	// 5. 调用快递鸟批量查价API
	a.logger.Info("🌐 开始调用快递鸟批量查价API")
	allPrices, err := a.callBatchPriceAPI(ctx, req)
	if err != nil {
		a.logger.Error("❌ 快递鸟批量查价API调用失败", zap.Error(err))
		return nil, fmt.Errorf("调用快递鸟API失败: %w", err)
	}

	a.logger.Info("✅ 快递鸟批量查价API调用成功",
		zap.Int("total_prices", len(allPrices)))

	// 6. 缓存所有价格数据
	a.cachePrices(routeKey, requestHash, allPrices)
	a.logger.Info("💾 价格数据已缓存",
		zap.String("route_key", routeKey),
		zap.Int("cached_prices", len(allPrices)))

	// 7. 过滤出请求的快递公司价格
	filteredPrices, err := a.filterPricesByExpressType(allPrices, req.ExpressType)
	if err != nil {
		a.logger.Error("❌ 过滤价格失败", zap.Error(err))
		return nil, err
	}

	a.logger.Info("🎯 价格过滤完成",
		zap.String("express_type", req.ExpressType),
		zap.Int("filtered_prices", len(filteredPrices)))

	return filteredPrices, nil
}

// validatePriceRequest 验证价格查询请求
func (a *KuaidiNiaoAdapter) validatePriceRequest(req *model.PriceRequest) error {
	if req.Sender.Province == "" || req.Receiver.Province == "" {
		return fmt.Errorf("寄件地址和收件地址不能为空")
	}
	if req.Sender.City == "" || req.Receiver.City == "" {
		return fmt.Errorf("寄件城市和收件城市不能为空")
	}
	if req.Package.Weight <= 0 {
		return fmt.Errorf("重量必须大于0")
	}
	return nil
}

// convertToPriceAPIRequest 转换为快递鸟价格查询API请求格式
func (a *KuaidiNiaoAdapter) convertToPriceAPIRequest(req *model.PriceRequest) *KuaidiNiaoPriceRequest {
	// 标准化地址
	senderProvince := a.standardizeProvince(req.Sender.Province)
	senderCity := a.standardizeCity(req.Sender.City)
	receiverProvince := a.standardizeProvince(req.Receiver.Province)
	receiverCity := a.standardizeCity(req.Receiver.City)

	// 🔥 计算计费重量（考虑体积重量）
	chargedWeight := a.calculateChargedWeight(req, "")

	apiReq := &KuaidiNiaoPriceRequest{
		TransportType: 1,             // 快递类
		ShipperType:   5,             // 当日收(可根据业务需求调整)
		Weight:        chargedWeight, // 🔥 使用计费重量而不是实际重量
		Receiver: KuaidiNiaoAddressInfo{
			ProvinceName: receiverProvince,
			CityName:     receiverCity,
			ExpAreaName:  req.Receiver.District,
		},
		Sender: KuaidiNiaoAddressInfo{
			ProvinceName: senderProvince,
			CityName:     senderCity,
			ExpAreaName:  req.Sender.District,
		},
	}

	// 如果有声明价值，添加保价
	if req.Package.InsureValue > 0 && req.Package.InsureValue <= 3000 {
		apiReq.InsureAmount = req.Package.InsureValue
	}

	return apiReq
}

// callPriceAPIWithRetry 调用价格查询API(带重试)
func (a *KuaidiNiaoAdapter) callPriceAPIWithRetry(ctx context.Context, req *KuaidiNiaoPriceRequest) (*KuaidiNiaoPriceResponse, error) {
	const maxRetries = 3
	const baseDelay = time.Second

	for attempt := 0; attempt <= maxRetries; attempt++ {
		resp, err := a.callPriceAPI(ctx, req)

		// 成功或非重试错误直接返回
		if err == nil || !a.isRetryableError(err) {
			return resp, err
		}

		// 最后一次尝试失败
		if attempt == maxRetries {
			return nil, fmt.Errorf("重试%d次后仍然失败: %w", maxRetries, err)
		}

		// 指数退避
		delay := baseDelay * time.Duration(1<<attempt)
		a.logger.Warn("价格查询API调用失败，准备重试",
			zap.Int("attempt", attempt+1),
			zap.Duration("delay", delay),
			zap.Error(err))

		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(delay):
			// 继续重试
		}
	}

	return nil, fmt.Errorf("不应该到达这里")
}

// callPriceAPI 调用价格查询API
func (a *KuaidiNiaoAdapter) callPriceAPI(ctx context.Context, req *KuaidiNiaoPriceRequest) (*KuaidiNiaoPriceResponse, error) {
	// 序列化请求数据
	requestData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 构建系统级参数
	systemParams := KuaidiNiaoSystemParams{
		RequestType: "1815", // 价格查询接口指令
		EBusinessID: a.config.EBusinessID,
		RequestData: string(requestData),
		DataType:    "2",
		DataSign:    a.generateSignature(string(requestData)),
	}

	// 调用API
	respData, err := a.callAPI(ctx, systemParams)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var apiResp KuaidiNiaoPriceResponse
	if err := json.Unmarshal(respData, &apiResp); err != nil {
		return nil, fmt.Errorf("解析价格查询响应失败: %w", err)
	}

	// 检查业务层面的错误
	if !apiResp.Success {
		return nil, fmt.Errorf("快递鸟价格查询失败 [%s]: %s", apiResp.ResultCode, apiResp.Reason)
	}

	return &apiResp, nil
}

// callRegionCheckAPI 调用超区校验接口（接口指令1814）
func (a *KuaidiNiaoAdapter) callRegionCheckAPI(ctx context.Context, req *KuaidiNiaoRegionCheckRequest) (*KuaidiNiaoRegionCheckResponse, error) {
	// 序列化请求数据
	requestData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化超区校验请求数据失败: %w", err)
	}

	// 构建系统级参数
	systemParams := KuaidiNiaoSystemParams{
		RequestType: "1814", // 超区校验接口指令
		EBusinessID: a.config.EBusinessID,
		RequestData: string(requestData),
		DataType:    "2",
		DataSign:    a.generateSignature(string(requestData)),
	}

	// 调用API
	respData, err := a.callAPI(ctx, systemParams)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var apiResp KuaidiNiaoRegionCheckResponse
	if err := json.Unmarshal(respData, &apiResp); err != nil {
		return nil, fmt.Errorf("解析超区校验响应失败: %w", err)
	}

	// 检查业务层面的错误
	if !apiResp.Success {
		return nil, fmt.Errorf("快递鸟超区校验失败 [%s]: %s", apiResp.ResultCode, apiResp.Reason)
	}

	return &apiResp, nil
}

// getPickupTimeInfo 获取快递鸟预约时间信息
func (a *KuaidiNiaoAdapter) getPickupTimeInfo(ctx context.Context, req *model.PriceRequest) *model.PickupTimeInfo {
	// 🔥 计算计费重量（考虑体积重量）
	chargedWeight := a.calculateChargedWeight(req, "")

	// 构建超区校验请求
	regionCheckReq := &KuaidiNiaoRegionCheckRequest{
		TransportType: 1,             // 快递类
		ShipperType:   5,             // 当日收
		Weight:        chargedWeight, // 🔥 使用计费重量
		Receiver: KuaidiNiaoAddressInfo{
			ProvinceName: a.standardizeProvince(req.Receiver.Province),
			CityName:     a.standardizeCity(req.Receiver.City),
			ExpAreaName:  req.Receiver.District,
		},
		Sender: KuaidiNiaoAddressInfo{
			ProvinceName: a.standardizeProvince(req.Sender.Province),
			CityName:     a.standardizeCity(req.Sender.City),
			ExpAreaName:  req.Sender.District,
		},
	}

	// 调用超区校验接口
	resp, err := a.callRegionCheckAPI(ctx, regionCheckReq)
	if err != nil {
		a.logger.Warn("快递鸟超区校验接口调用失败，使用默认预约时间信息",
			zap.Error(err))
		return a.getDefaultPickupTimeInfo()
	}

	// 转换为标准格式
	return a.convertToStandardPickupTimeInfo(resp)
}

// getDefaultPickupTimeInfo 获取默认预约时间信息
func (a *KuaidiNiaoAdapter) getDefaultPickupTimeInfo() *model.PickupTimeInfo {
	// 生成默认的预约时间段（当天和明天）
	now := time.Now()
	today := now.Format("2006-01-02")
	tomorrow := now.AddDate(0, 0, 1).Format("2006-01-02")

	return &model.PickupTimeInfo{
		PickupRequired:     true,
		SupportsPickupCode: false,
		MinAdvanceHours:    2,
		TimeFormat:         "YYYY-MM-DD HH:mm:ss",
		AvailableSlots: []model.PickupTimeSlotSimple{
			{
				SlotID:    "today_09_18",
				SlotName:  "今天 09:00-18:00",
				StartTime: today + " 09:00:00",
				EndTime:   today + " 18:00:00",
			},
			{
				SlotID:    "tomorrow_09_18",
				SlotName:  "明天 09:00-18:00",
				StartTime: tomorrow + " 09:00:00",
				EndTime:   tomorrow + " 18:00:00",
			},
		},
	}
}

// convertToStandardPickupTimeInfo 转换快递鸟预约时间信息为标准格式
func (a *KuaidiNiaoAdapter) convertToStandardPickupTimeInfo(resp *KuaidiNiaoRegionCheckResponse) *model.PickupTimeInfo {
	var availableSlots []model.PickupTimeSlotSimple

	// 遍历所有支持的快递公司
	for _, data := range resp.Data {
		if !data.IsSupport {
			continue
		}

		// 转换时间段
		for _, slot := range data.AvailableTimeSlots {
			availableSlots = append(availableSlots, model.PickupTimeSlotSimple{
				SlotID:    slot.SlotID,
				SlotName:  slot.SlotName,
				StartTime: slot.StartTime,
				EndTime:   slot.EndTime,
			})
		}
	}

	// 如果没有获取到时间段，使用默认时间段
	if len(availableSlots) == 0 {
		return a.getDefaultPickupTimeInfo()
	}

	return &model.PickupTimeInfo{
		PickupRequired:     true,
		SupportsPickupCode: false,
		MinAdvanceHours:    2,
		TimeFormat:         "YYYY-MM-DD HH:mm:ss",
		AvailableSlots:     availableSlots,
	}
}

// convertToStandardizedPrices 转换为标准化价格格式
func (a *KuaidiNiaoAdapter) convertToStandardizedPrices(apiResp *KuaidiNiaoPriceResponse, req *model.PriceRequest) []model.StandardizedPrice {
	var prices []model.StandardizedPrice

	// 🔥 新增：获取预约时间信息
	pickupTimeInfo := a.getPickupTimeInfo(context.Background(), req)

	for _, item := range apiResp.Data {
		// 计算计费重量
		chargedWeight := a.calculateChargedWeight(req, item.ShipperCode)

		// 解析价格
		cost, _ := strconv.ParseFloat(item.Cost, 64)

		// 获取快递公司中文名
		expressName := KuaidiNiaoExpressMapping[item.ShipperCode]
		if expressName == "" {
			expressName = item.ShipperCode
		}

		// 生成订单代码用于下单验证
		orderCode := fmt.Sprintf("KDN_%s_%d", item.ShipperCode, time.Now().Unix())

		// 🔧 新增：基于服务类型生成产品代码和产品名称
		serviceType, serviceName := a.getServiceTypeInfo()
		productCode := fmt.Sprintf("KDN_%s_%s", item.ShipperCode, serviceType)
		productName := fmt.Sprintf("%s%s", expressName, serviceName)

		// 🔧 详细调试日志：记录快递鸟原始价格数据
		a.logger.Info("💰 快递鸟原始价格数据",
			zap.String("provider", "kuaidiniao"),
			zap.String("express_code", item.ShipperCode),
			zap.String("express_name", expressName),
			zap.String("cost", item.Cost),
			zap.Float64("first_weight", item.FirstWeight),
			zap.Float64("first_weight_amount", item.FirstWeightAmount),
			zap.Float64("continuous_weight", item.ContinuousWeight),
			zap.Float64("continuous_weight_price", item.ContinuousWeightPrice),
			zap.Float64("continuous_weight_amount", item.ContinuousWeightAmount),
			zap.Float64("total_fee", item.TotalFee),
			zap.Float64("calc_weight", chargedWeight),
			zap.String("product_code", productCode),
			zap.String("product_name", productName))

		price := model.StandardizedPrice{
			Provider:             "kuaidiniao",
			ExpressCode:          item.ShipperCode,
			ExpressName:          expressName,
			ProductCode:          productCode, // ✅ 添加产品代码
			ProductName:          productName, // ✅ 添加产品名称
			Price:                cost,
			ContinuedWeightPerKg: item.ContinuousWeightPrice, // ✅ 添加续重价格
			OrderCode:            orderCode,
			PickupTimeInfo:       pickupTimeInfo, // 🔥 使用从超区校验接口获取的预约时间信息
			CalcWeight:           chargedWeight,
			EstimatedDays:        1, // 快递鸟一般1天到达
		}

		// 🔧 详细调试日志：记录转换后的标准价格数据
		a.logger.Info("🎯 快递鸟价格转换完成",
			zap.String("provider", "kuaidiniao"),
			zap.String("express_code", price.ExpressCode),
			zap.String("express_name", price.ExpressName),
			zap.String("product_code", price.ProductCode),
			zap.String("product_name", price.ProductName),
			zap.Float64("price", price.Price),
			zap.Float64("continued_weight_per_kg", price.ContinuedWeightPerKg),
			zap.Float64("calc_weight", price.CalcWeight),
			zap.String("order_code", price.OrderCode))

		prices = append(prices, price)
	}

	return prices
}

// getServiceTypeInfo 获取服务类型信息
// 根据快递鸟请求中使用的 ShipperType 返回对应的服务类型代码和名称
func (a *KuaidiNiaoAdapter) getServiceTypeInfo() (string, string) {
	// 目前快递鸟使用固定的 ShipperType: 5 (当日收)
	// 未来可以根据业务需求扩展支持不同服务类型
	return "SAME_DAY", "当日达快递"
}

// calculateChargedWeight 计算计费重量
func (a *KuaidiNiaoAdapter) calculateChargedWeight(req *model.PriceRequest, expressCode string) float64 {
	// 如果没有体积信息，直接返回实际重量
	if req.Package.Length <= 0 || req.Package.Width <= 0 || req.Package.Height <= 0 {
		return math.Ceil(req.Package.Weight)
	}

	// 计算体积（cm³）
	volumeCm3 := req.Package.Length * req.Package.Width * req.Package.Height

	// 🔥 从数据库获取快递公司的抛比配置
	volumeRatio := a.getVolumeWeightRatio(expressCode)

	// 计算体积重量
	volumeWeight := volumeCm3 / float64(volumeRatio)

	// 取实际重量和体积重量的较大值，向上取整
	chargedWeight := math.Max(req.Package.Weight, volumeWeight)
	return math.Ceil(chargedWeight)
}

// getVolumeWeightRatio 获取快递公司的抛比配置
func (a *KuaidiNiaoAdapter) getVolumeWeightRatio(expressCode string) int {
	// 如果没有指定快递公司代码，使用默认抛比8000
	if expressCode == "" {
		a.logger.Debug("快递鸟：未指定快递公司，使用默认抛比8000")
		return 8000
	}

	// 从数据库获取快递公司配置
	if a.expressCompanyRepo != nil {
		// 类型断言为 ExpressCompanyRepository
		if repo, ok := a.expressCompanyRepo.(interface {
			GetCompanyByCode(string) (*express.ExpressCompany, error)
		}); ok {
			company, err := repo.GetCompanyByCode(expressCode)
			if err != nil {
				a.logger.Warn("快递鸟：获取快递公司配置失败，使用默认抛比8000",
					zap.String("express_code", expressCode),
					zap.Error(err))
				return 8000
			}

			if !company.IsActive {
				a.logger.Warn("快递鸟：快递公司已禁用，使用默认抛比8000",
					zap.String("express_code", expressCode),
					zap.String("company_name", company.Name))
				return 8000
			}

			if company.VolumeWeightRatio <= 0 {
				a.logger.Warn("快递鸟：快递公司抛比配置无效，使用默认抛比8000",
					zap.String("express_code", expressCode),
					zap.String("company_name", company.Name),
					zap.Int("invalid_ratio", company.VolumeWeightRatio))
				return 8000
			}

			a.logger.Debug("快递鸟：从数据库获取抛比配置成功",
				zap.String("express_code", expressCode),
				zap.String("company_name", company.Name),
				zap.Int("volume_ratio", company.VolumeWeightRatio))

			return company.VolumeWeightRatio
		}
	}

	// 如果数据库仓库未初始化，使用默认抛比8000
	a.logger.Warn("快递鸟：数据库仓库未初始化，使用默认抛比8000")
	return 8000
}

// CreateOrder 创建订单 - 实现ProviderAdapter接口
func (a *KuaidiNiaoAdapter) CreateOrder(ctx context.Context, req *model.OrderRequest) (*model.OrderResult, error) {
	start := time.Now()

	a.logger.Info("快递鸟订单创建开始",
		zap.String("provider", "kuaidiniao"),
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("express_type", req.ExpressType))

	defer func() {
		duration := time.Since(start)
		a.logger.Info("快递鸟订单创建完成",
			zap.String("provider", "kuaidiniao"),
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.Duration("duration", duration))
	}()

	// 1. 参数验证
	if err := a.validateOrderRequest(req); err != nil {
		return nil, fmt.Errorf("订单参数验证失败: %w", err)
	}

	// 🔥 修复：检查快递公司启用状态
	if a.mappingService != nil {
		// 类型断言为ExpressMappingService接口
		if mappingService, ok := a.mappingService.(interface {
			GetProviderCompanyCode(ctx context.Context, companyCode, providerCode string) (string, error)
		}); ok {
			mappedCode, err := mappingService.GetProviderCompanyCode(ctx, req.ExpressType, "kuaidiniao")
			if err != nil {
				a.logger.Error("快递鸟不支持该快递公司或快递公司已禁用，无法创建订单",
					zap.String("express_type", req.ExpressType),
					zap.String("customer_order_no", req.CustomerOrderNo),
					zap.Error(err))
				return nil, fmt.Errorf("快递公司 %s 已禁用或不支持，无法创建订单", req.ExpressType)
			}
			// 使用数据库映射到的供应商代码作为下单代码
			req.ExpressType = mappedCode
		}
	}

	// 2. 限流控制
	if err := a.rateLimiter.Wait(ctx); err != nil {
		return nil, fmt.Errorf("限流等待失败: %w", err)
	}

	// 3. 转换为快递鸟API格式
	apiReq := a.convertToOrderAPIRequest(req)

	// 4. 调用快递鸟API
	apiResp, err := a.callCreateOrderAPIWithRetry(ctx, apiReq)
	if err != nil {
		return nil, fmt.Errorf("调用创建订单API失败: %w", err)
	}

	// 5. 转换为标准格式
	result := a.convertToOrderResult(apiResp, req)

	// 🔧 修复：检查转换结果是否为空，防止nil pointer dereference
	if result == nil {
		return nil, fmt.Errorf("快递鸟订单创建失败: API响应数据为空或格式错误")
	}

	a.logger.Info("快递鸟订单创建成功",
		zap.String("provider", "kuaidiniao"),
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("order_no", result.OrderNo),
		zap.String("kdn_order_code", result.TrackingNo))

	return result, nil
}

// validateOrderRequest 验证订单创建请求
func (a *KuaidiNiaoAdapter) validateOrderRequest(req *model.OrderRequest) error {
	if req.CustomerOrderNo == "" {
		return fmt.Errorf("客户订单号不能为空")
	}
	if req.Sender.Name == "" || req.Sender.Mobile == "" {
		return fmt.Errorf("寄件人信息不完整")
	}
	if req.Receiver.Name == "" || req.Receiver.Mobile == "" {
		return fmt.Errorf("收件人信息不完整")
	}
	if req.Sender.Address == "" || req.Receiver.Address == "" {
		return fmt.Errorf("寄收件地址不能为空")
	}
	if req.Package.GoodsName == "" {
		return fmt.Errorf("商品名称不能为空")
	}
	return nil
}

// convertToOrderAPIRequest 转换为快递鸟订单创建API请求格式
func (a *KuaidiNiaoAdapter) convertToOrderAPIRequest(req *model.OrderRequest) *KuaidiNiaoOrderRequest {
	// 标准化地址
	senderProvince := a.standardizeProvince(req.Sender.Province)
	senderCity := a.standardizeCity(req.Sender.City)
	receiverProvince := a.standardizeProvince(req.Receiver.Province)
	receiverCity := a.standardizeCity(req.Receiver.City)

	// 🔥 计算计费重量（考虑体积重量）
	// 将OrderRequest转换为PriceRequest格式来计算重量
	priceReq := &model.PriceRequest{
		Package: req.Package,
	}
	chargedWeight := a.calculateChargedWeight(priceReq, req.ExpressType)

	// 🔥 修复：综合处理Mobile和Tel字段，符合快递鸟API要求
	senderTel, senderMobile := a.formatPhoneForKuaidiNiaoComprehensive(req.Sender.Mobile, req.Sender.Tel)
	receiverTel, receiverMobile := a.formatPhoneForKuaidiNiaoComprehensive(req.Receiver.Mobile, req.Receiver.Tel)

	apiReq := &KuaidiNiaoOrderRequest{
		TransportType: 1,                   // 快递类
		ShipperType:   5,                   // 当日收
		OrderCode:     req.PlatformOrderNo, // 🔥 修复：使用平台订单号而不是客户订单号（与其他供应商保持一致）
		ExpType:       1,                   // 快递类型
		PayType:       3,                   // 月结
		Receiver: KuaidiNiaoReceiverInfo{
			Name:         req.Receiver.Name,
			Mobile:       receiverMobile,
			Tel:          receiverTel,
			ProvinceName: receiverProvince,
			CityName:     receiverCity,
			ExpAreaName:  req.Receiver.District,
			Address:      req.Receiver.Address,
		},
		Sender: KuaidiNiaoSenderInfo{
			Name:         req.Sender.Name,
			Mobile:       senderMobile,
			Tel:          senderTel,
			ProvinceName: senderProvince,
			CityName:     senderCity,
			ExpAreaName:  req.Sender.District,
			Address:      req.Sender.Address,
		},
		Commodity: []KuaidiNiaoCommodityInfo{
			{
				GoodsName:     req.Package.GoodsName,
				GoodsQuantity: req.Package.Quantity,
				GoodsPrice:    req.Package.InsureValue,
			},
		},
	}

	// 如果指定了快递公司，设置ShipperCode（此时req.ExpressType已在上游通过数据库映射为供应商代码）
	if req.ExpressType != "" {
		apiReq.ShipperCode = req.ExpressType
	}

	// 设置重量和数量
	if req.Package.Weight > 0 {
		apiReq.Weight = chargedWeight // 🔥 使用计费重量而不是实际重量
	}
	if req.Package.Quantity > 0 {
		apiReq.Quantity = req.Package.Quantity
	} else {
		apiReq.Quantity = 1 // 默认1件
	}

	// 设置体积
	if req.Package.Length > 0 && req.Package.Width > 0 && req.Package.Height > 0 {
		// 快递鸟API要求体积单位为立方厘米
		volumeCm3 := req.Package.Length * req.Package.Width * req.Package.Height
		apiReq.Volume = volumeCm3
	}

	// 🔧 临时修复：为避免105错误(请选择三日内固定时段下单)，暂时不传StartDate和EndDate
	// 让快递鸟系统默认分配最近时间上门取件
	// TODO: 实现超区校验接口(RequestType=1814)获取可预约时间段后再恢复用户指定时间功能
	a.logger.Info("快递鸟下单：临时使用系统默认预约时间避免105错误",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("user_provided_start_time", req.Pickup.StartTime),
		zap.String("user_provided_end_time", req.Pickup.EndTime),
		zap.String("note", "暂时不传StartDate和EndDate，由快递鸟系统默认分配时间，避免固定时段错误"))

	// 设置备注
	if req.Package.Remark != "" {
		apiReq.Remark = req.Package.Remark
	}

	// 🔧 新增：设置回调URL，统一由系统配置决定
	callbackManager := config.GetCallbackURLManager()
	callbackURL := callbackManager.GetCallbackURL("kuaidiniao")
	apiReq.NotifyUrl = callbackURL

	a.logger.Info("快递鸟下单设置回调URL",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("callback_url", callbackURL))

	// 设置声明价值
	if req.Package.InsureValue > 0 && req.Package.InsureValue <= 3000 {
		apiReq.InsureAmount = req.Package.InsureValue
	}

	// 设置实际付款金额
	if req.Package.InsureValue > 0 {
		apiReq.ActualPaymentAmount = req.Package.InsureValue
	}

	return apiReq
}

// callCreateOrderAPIWithRetry 调用创建订单API(带重试)
func (a *KuaidiNiaoAdapter) callCreateOrderAPIWithRetry(ctx context.Context, req *KuaidiNiaoOrderRequest) (*KuaidiNiaoOrderResponse, error) {
	const maxRetries = 3
	const baseDelay = time.Second

	for attempt := 0; attempt <= maxRetries; attempt++ {
		resp, err := a.callCreateOrderAPI(ctx, req)

		// 成功或非重试错误直接返回
		if err == nil || !a.isRetryableError(err) {
			return resp, err
		}

		// 最后一次尝试失败
		if attempt == maxRetries {
			return nil, fmt.Errorf("重试%d次后仍然失败: %w", maxRetries, err)
		}

		// 指数退避
		delay := baseDelay * time.Duration(1<<attempt)
		a.logger.Warn("创建订单API调用失败，准备重试",
			zap.Int("attempt", attempt+1),
			zap.Duration("delay", delay),
			zap.Error(err))

		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(delay):
			// 继续重试
		}
	}

	return nil, fmt.Errorf("不应该到达这里")
}

// callCreateOrderAPI 调用创建订单API
func (a *KuaidiNiaoAdapter) callCreateOrderAPI(ctx context.Context, req *KuaidiNiaoOrderRequest) (*KuaidiNiaoOrderResponse, error) {
	// 序列化请求数据
	requestData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 构建系统级参数
	systemParams := KuaidiNiaoSystemParams{
		RequestType: "1801", // 订单创建接口指令
		EBusinessID: a.config.EBusinessID,
		RequestData: string(requestData),
		DataType:    "2",
		DataSign:    a.generateSignature(string(requestData)),
	}

	// 调用API
	respData, err := a.callAPI(ctx, systemParams)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var apiResp KuaidiNiaoOrderResponse
	if err := json.Unmarshal(respData, &apiResp); err != nil {
		return nil, fmt.Errorf("解析订单创建响应失败: %w", err)
	}

	// 检查业务层面的错误
	if !apiResp.Success {
		return nil, fmt.Errorf("快递鸟订单创建失败 [%s]: %s", apiResp.ResultCode, apiResp.Reason)
	}

	return &apiResp, nil
}

// convertToOrderResult 转换为标准订单结果格式
func (a *KuaidiNiaoAdapter) convertToOrderResult(apiResp *KuaidiNiaoOrderResponse, req *model.OrderRequest) *model.OrderResult {
	// 🔧 修复：检查API响应是否为空
	if apiResp == nil {
		a.logger.Error("快递鸟API响应为nil",
			zap.String("customer_order_no", req.CustomerOrderNo))
		return nil
	}

	// 🔧 修复：检查Order字段是否为空，根据官方API文档，订单数据在Order字段中
	if apiResp.Order.OrderCode == "" || apiResp.Order.KDNOrderCode == "" {
		a.logger.Error("快递鸟API响应Order字段为空或缺少必要信息",
			zap.String("customer_order_no", req.CustomerOrderNo),
			zap.Bool("success", apiResp.Success),
			zap.String("result_code", apiResp.ResultCode),
			zap.String("reason", apiResp.Reason),
			zap.String("order_code", apiResp.Order.OrderCode),
			zap.String("kdn_order_code", apiResp.Order.KDNOrderCode))
		return nil
	}

	// 使用Order字段中的数据
	orderData := apiResp.Order

	result := &model.OrderResult{
		PlatformOrderNo: req.CustomerOrderNo,
		CustomerOrderNo: req.CustomerOrderNo,
		OrderNo:         orderData.OrderCode,
		TrackingNo:      orderData.KDNOrderCode, // 快递鸟订单号作为跟踪号
		Price:           0,                      // 快递鸟下单API不返回费用，费用通过回调获取
	}

	a.logger.Info("快递鸟订单结果转换完成",
		zap.String("customer_order_no", req.CustomerOrderNo),
		zap.String("kdn_order_code", orderData.KDNOrderCode),
		zap.String("order_code", orderData.OrderCode),
		zap.String("start_date", apiResp.StartDate),
		zap.String("end_date", apiResp.EndDate))

	return result
}

// generateSignature 生成签名
func (a *KuaidiNiaoAdapter) generateSignature(requestData string) string {
	// 拼接字符串: RequestData + ApiKey
	signData := requestData + a.config.ApiKey

	// MD5加密(32位小写)
	h := md5.New()
	h.Write([]byte(signData))
	md5str := fmt.Sprintf("%x", h.Sum(nil))

	// Base64编码
	return base64.StdEncoding.EncodeToString([]byte(md5str))
}

// callAPI 通用API调用方法
func (a *KuaidiNiaoAdapter) callAPI(ctx context.Context, params KuaidiNiaoSystemParams) ([]byte, error) {
	// 构建表单数据
	formData := url.Values{}
	formData.Set("RequestType", params.RequestType)
	formData.Set("EBusinessID", params.EBusinessID)
	formData.Set("RequestData", params.RequestData)
	formData.Set("DataSign", params.DataSign)
	formData.Set("DataType", params.DataType)

	// 创建HTTP请求
	req, err := http.NewRequestWithContext(ctx, "POST", a.config.BaseURL, strings.NewReader(formData.Encode()))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/x-www-form-urlencoded;charset=utf-8")

	// 发送请求
	resp, err := a.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respData, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应数据失败: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API返回错误状态码: %d, 响应: %s", resp.StatusCode, string(respData))
	}

	return respData, nil
}

// isRetryableError 判断是否为可重试错误
func (a *KuaidiNiaoAdapter) isRetryableError(err error) bool {
	errStr := err.Error()
	return strings.Contains(errStr, "timeout") ||
		strings.Contains(errStr, "connection") ||
		strings.Contains(errStr, "503") ||
		strings.Contains(errStr, "502") ||
		strings.Contains(errStr, "500")
}

// 地址标准化方法
func (a *KuaidiNiaoAdapter) standardizeProvince(province string) string {
	provinceMap := map[string]string{
		"北京":  "北京市",
		"上海":  "上海市",
		"天津":  "天津市",
		"重庆":  "重庆市",
		"内蒙古": "内蒙古自治区",
		"广西":  "广西壮族自治区",
		"西藏":  "西藏自治区",
		"宁夏":  "宁夏回族自治区",
		"新疆":  "新疆维吾尔自治区",
	}

	if standard, exists := provinceMap[province]; exists {
		return standard
	}

	// 如果已经是标准格式，直接返回
	if strings.HasSuffix(province, "省") ||
		strings.HasSuffix(province, "市") ||
		strings.HasSuffix(province, "自治区") {
		return province
	}

	// 默认添加"省"后缀
	return province + "省"
}

func (a *KuaidiNiaoAdapter) standardizeCity(city string) string {
	// 如果已经是标准格式，直接返回
	if strings.HasSuffix(city, "市") ||
		strings.HasSuffix(city, "州") ||
		strings.HasSuffix(city, "盟") {
		return city
	}

	// 默认添加"市"后缀
	return city + "市"
}

// CancelOrder 取消订单 - 实现ProviderAdapter接口
// 🔥 新增：快递鸟主动查询确认机制，解决不推送已取消状态的问题
func (a *KuaidiNiaoAdapter) CancelOrder(ctx context.Context, taskId string, orderNo string, reason string) error {
	start := time.Now()

	a.logger.Info("🚀 [快递鸟取消] 订单取消流程开始",
		zap.String("provider", "kuaidiniao"),
		zap.String("task_id", taskId),
		zap.String("order_no", orderNo),
		zap.String("reason", reason),
		zap.String("step", "1_start"))

	defer func() {
		duration := time.Since(start)
		a.logger.Info("🏁 [快递鸟取消] 订单取消流程结束",
			zap.String("provider", "kuaidiniao"),
			zap.String("order_no", orderNo),
			zap.Duration("total_duration", duration),
			zap.String("step", "9_end"))
	}()

	// 1. 参数验证
	a.logger.Info("🔍 [快递鸟取消] 参数验证",
		zap.String("order_no", orderNo),
		zap.String("reason", reason),
		zap.String("step", "2_validation"))

	if orderNo == "" {
		a.logger.Error("❌ [快递鸟取消] 参数验证失败：订单号为空", zap.String("step", "2_validation_failed"))
		return fmt.Errorf("订单号不能为空")
	}
	if reason == "" {
		reason = "用户取消"
		a.logger.Info("📝 [快递鸟取消] 使用默认取消原因", zap.String("default_reason", reason))
	}

	// 2. 限流控制
	a.logger.Info("⏳ [快递鸟取消] 限流控制检查", zap.String("step", "3_rate_limit"))
	if err := a.rateLimiter.Wait(ctx); err != nil {
		a.logger.Error("❌ [快递鸟取消] 限流等待失败", zap.Error(err), zap.String("step", "3_rate_limit_failed"))
		return fmt.Errorf("限流等待失败: %w", err)
	}

	// 3. 构建取消请求
	a.logger.Info("📦 [快递鸟取消] 构建取消请求", zap.String("step", "4_build_request"))
	cancelReq := &KuaidiNiaoCancelRequest{
		OrderCode:  orderNo,
		CancelType: 11, // 其他
		CancelMsg:  reason,
	}
	a.logger.Info("📋 [快递鸟取消] 取消请求详情",
		zap.String("order_code", cancelReq.OrderCode),
		zap.Int("cancel_type", cancelReq.CancelType),
		zap.String("cancel_msg", cancelReq.CancelMsg))

	// 4. 调用快递鸟取消API
	a.logger.Info("🌐 [快递鸟取消] 开始调用快递鸟取消API", zap.String("step", "5_call_api"))
	err := a.callCancelOrderAPIWithRetry(ctx, cancelReq)
	if err != nil {
		a.logger.Error("❌ [快递鸟取消] 调用取消API失败",
			zap.Error(err),
			zap.String("step", "5_call_api_failed"))
		return fmt.Errorf("调用取消订单API失败: %w", err)
	}

	a.logger.Info("✅ [快递鸟取消] 快递鸟取消API调用成功",
		zap.String("provider", "kuaidiniao"),
		zap.String("order_no", orderNo),
		zap.String("step", "6_api_success"))

	// 🔥 修改：不再进行主动查询确认，直接返回成功
	// 订单状态将在取消服务中直接设置为已取消
	a.logger.Info("🎉 [快递鸟取消] 订单取消API调用成功，状态将由取消服务直接设置为已取消",
		zap.String("provider", "kuaidiniao"),
		zap.String("order_no", orderNo),
		zap.String("step", "7_success"))

	return nil
}

// callCancelOrderAPIWithRetry 调用取消订单API(带重试)
func (a *KuaidiNiaoAdapter) callCancelOrderAPIWithRetry(ctx context.Context, req *KuaidiNiaoCancelRequest) error {
	const maxRetries = 3
	const baseDelay = time.Second

	for attempt := 0; attempt <= maxRetries; attempt++ {
		err := a.callCancelOrderAPI(ctx, req)

		// 成功或非重试错误直接返回
		if err == nil || !a.isRetryableError(err) {
			return err
		}

		// 最后一次尝试失败
		if attempt == maxRetries {
			return fmt.Errorf("重试%d次后仍然失败: %w", maxRetries, err)
		}

		// 指数退避
		delay := baseDelay * time.Duration(1<<attempt)
		a.logger.Warn("取消订单API调用失败，准备重试",
			zap.Int("attempt", attempt+1),
			zap.Duration("delay", delay),
			zap.Error(err))

		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(delay):
			// 继续重试
		}
	}

	return fmt.Errorf("不应该到达这里")
}

// callCancelOrderAPI 调用取消订单API
func (a *KuaidiNiaoAdapter) callCancelOrderAPI(ctx context.Context, req *KuaidiNiaoCancelRequest) error {
	// 序列化请求数据
	requestData, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 构建系统级参数
	systemParams := KuaidiNiaoSystemParams{
		RequestType: "1802", // 订单取消接口指令
		EBusinessID: a.config.EBusinessID,
		RequestData: string(requestData),
		DataType:    "2",
		DataSign:    a.generateSignature(string(requestData)),
	}

	// 调用API
	respData, err := a.callAPI(ctx, systemParams)
	if err != nil {
		return err
	}

	// 解析响应
	var apiResp KuaidiNiaoCancelResponse
	if err := json.Unmarshal(respData, &apiResp); err != nil {
		return fmt.Errorf("解析取消订单响应失败: %w", err)
	}

	// 检查业务层面的错误
	if !apiResp.Success {
		return fmt.Errorf("快递鸟订单取消失败 [%s]: %s", apiResp.ResultCode, apiResp.Reason)
	}

	return nil
}

// QueryOrder 查询订单 - 实现ProviderAdapter接口
func (a *KuaidiNiaoAdapter) QueryOrder(ctx context.Context, orderNo string, trackingNo string) (*model.OrderInfo, error) {
	start := time.Now()

	a.logger.Info("快递鸟订单信息查询开始",
		zap.String("provider", "kuaidiniao"),
		zap.String("order_no", orderNo),
		zap.String("tracking_no", trackingNo))

	defer func() {
		duration := time.Since(start)
		a.logger.Info("快递鸟订单信息查询完成",
			zap.String("provider", "kuaidiniao"),
			zap.String("order_no", orderNo),
			zap.Duration("duration", duration))
	}()

	// 1. 参数验证
	if orderNo == "" {
		return nil, fmt.Errorf("订单号不能为空")
	}

	// 2. 限流控制
	if err := a.rateLimiter.Wait(ctx); err != nil {
		return nil, fmt.Errorf("限流等待失败: %w", err)
	}

	// 3. 调用快递鸟订单信息查询API（接口指令1804）
	orderInfo, err := a.callOrderQueryAPIWithRetry(ctx, orderNo)
	if err != nil {
		return nil, fmt.Errorf("调用订单信息查询API失败: %w", err)
	}

	a.logger.Info("快递鸟订单信息查询成功",
		zap.String("provider", "kuaidiniao"),
		zap.String("order_no", orderNo),
		zap.String("status", orderInfo.Status))

	return orderInfo, nil
}

// QueryTrack 查询物流轨迹 - 实现ProviderAdapter接口
func (a *KuaidiNiaoAdapter) QueryTrack(ctx context.Context, trackingNo, expressType, phone, pollToken string, from, to string) (*model.TrackInfo, error) {
	start := time.Now()

	a.logger.Info("快递鸟物流跟踪查询开始",
		zap.String("provider", "kuaidiniao"),
		zap.String("tracking_no", trackingNo),
		zap.String("express_type", expressType))

	defer func() {
		duration := time.Since(start)
		a.logger.Info("快递鸟物流跟踪查询完成",
			zap.String("provider", "kuaidiniao"),
			zap.String("tracking_no", trackingNo),
			zap.Duration("duration", duration))
	}()

	// 1. 参数验证
	if trackingNo == "" {
		return nil, fmt.Errorf("运单号不能为空")
	}

	// 2. 限流控制
	if err := a.rateLimiter.Wait(ctx); err != nil {
		return nil, fmt.Errorf("限流等待失败: %w", err)
	}

	// 3. 构建查询请求
	trackReq := &KuaidiNiaoTrackRequest{
		LogisticCode: trackingNo,
		ShipperCode:  expressType,
		Sort:         0, // 按时间升序
	}

	// 如果提供了手机号后四位，设置CustomerName
	if phone != "" && len(phone) >= 4 {
		trackReq.CustomerName = phone[len(phone)-4:]
	}

	// 4. 调用快递鸟API
	apiResp, err := a.callTrackAPIWithRetry(ctx, trackReq)
	if err != nil {
		return nil, fmt.Errorf("调用物流跟踪API失败: %w", err)
	}

	// 5. 转换为标准格式
	trackInfo := a.convertToTrackInfo(apiResp)

	a.logger.Info("快递鸟物流跟踪查询成功",
		zap.String("provider", "kuaidiniao"),
		zap.String("tracking_no", trackingNo),
		zap.String("status", trackInfo.State))

	return trackInfo, nil
}

// callTrackAPIWithRetry 调用物流跟踪API(带重试)
func (a *KuaidiNiaoAdapter) callTrackAPIWithRetry(ctx context.Context, req *KuaidiNiaoTrackRequest) (*KuaidiNiaoTrackResponse, error) {
	const maxRetries = 3
	const baseDelay = time.Second

	for attempt := 0; attempt <= maxRetries; attempt++ {
		resp, err := a.callTrackAPI(ctx, req)

		// 成功或非重试错误直接返回
		if err == nil || !a.isRetryableError(err) {
			return resp, err
		}

		// 最后一次尝试失败
		if attempt == maxRetries {
			return nil, fmt.Errorf("重试%d次后仍然失败: %w", maxRetries, err)
		}

		// 指数退避
		delay := baseDelay * time.Duration(1<<attempt)
		a.logger.Warn("物流跟踪API调用失败，准备重试",
			zap.Int("attempt", attempt+1),
			zap.Duration("delay", delay),
			zap.Error(err))

		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(delay):
			// 继续重试
		}
	}

	return nil, fmt.Errorf("不应该到达这里")
}

// callTrackAPI 调用物流跟踪API
func (a *KuaidiNiaoAdapter) callTrackAPI(ctx context.Context, req *KuaidiNiaoTrackRequest) (*KuaidiNiaoTrackResponse, error) {
	// 序列化请求数据
	requestData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 构建系统级参数
	systemParams := KuaidiNiaoSystemParams{
		RequestType: "8001", // 物流跟踪接口指令
		EBusinessID: a.config.EBusinessID,
		RequestData: string(requestData),
		DataType:    "2",
		DataSign:    a.generateSignature(string(requestData)),
	}

	// 调用API
	respData, err := a.callAPI(ctx, systemParams)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var apiResp KuaidiNiaoTrackResponse
	if err := json.Unmarshal(respData, &apiResp); err != nil {
		return nil, fmt.Errorf("解析物流跟踪响应失败: %w", err)
	}

	// 检查业务层面的错误
	if !apiResp.Success {
		// 如果是"暂无轨迹信息"，返回空轨迹而不是错误
		if strings.Contains(apiResp.Reason, "暂无轨迹") || apiResp.State == "0" {
			return &apiResp, nil
		}
		return nil, fmt.Errorf("快递鸟物流跟踪查询失败: %s", apiResp.Reason)
	}

	return &apiResp, nil
}

// convertToTrackInfo 转换为标准物流跟踪信息格式
func (a *KuaidiNiaoAdapter) convertToTrackInfo(apiResp *KuaidiNiaoTrackResponse) *model.TrackInfo {
	// 映射状态
	status := a.mapTrackStatus(apiResp.State, apiResp.StateEx)
	statusDesc := a.getStatusDescription(apiResp.State, apiResp.StateEx)

	trackInfo := &model.TrackInfo{
		TrackingNo:  apiResp.LogisticCode,
		ExpressType: apiResp.ShipperCode,
		State:       status,
		StateDesc:   statusDesc,
	}

	// 转换轨迹列表
	for _, item := range apiResp.Traces {
		// 解析时间
		acceptTime, err := a.parseTrackTime(item.AcceptTime)
		if err != nil {
			// 如果时间解析失败，使用当前时间
			acceptTime = util.NowBeijing()
		}

		track := &model.TrackItem{
			Time:    acceptTime,
			Context: item.AcceptStation,
			Status:  a.mapTrackStatus(item.Action, item.Action),
		}

		trackInfo.Tracks = append(trackInfo.Tracks, track)
	}

	// 快递鸟API的派件信息存储在轨迹数据中，这里无需额外设置

	return trackInfo
}

// mapTrackStatus 映射轨迹状态
func (a *KuaidiNiaoAdapter) mapTrackStatus(state, stateEx string) string {
	// 优先使用详细状态
	if stateEx != "" {
		switch stateEx {
		case "1":
			return "picked_up" // 已揽收
		case "2":
			return "in_transit" // 在途中
		case "3":
			return "delivered" // 已签收
		case "201":
			return "dispatching" // 到达派件城市
		case "202":
			return "delivering" // 派件中
		case "204":
			return "in_transit" // 到达中转站
		case "301":
			return "exception" // 异常
		case "302":
			return "delivered" // 正常签收
		case "304":
			return "rejected" // 拒收
		case "311":
			return "lost" // 丢失
		case "412":
			return "stored" // 代收点暂存
		}
	}

	// 使用基础状态
	switch state {
	case "0":
		return "no_info" // 暂无物流信息
	case "1":
		return "picked_up" // 已揽收
	case "2":
		return "in_transit" // 在途中
	case "3":
		return "delivered" // 已签收
	case "4":
		return "exception" // 异常
	default:
		return "unknown" // 未知状态
	}
}

// getStatusDescription 获取状态描述
func (a *KuaidiNiaoAdapter) getStatusDescription(state, stateEx string) string {
	statusMap := map[string]string{
		"0": "暂无物流信息",
		"1": "已揽收",
		"2": "在途中",
		"3": "已签收",
		"4": "异常",
	}

	if desc, exists := statusMap[state]; exists {
		return desc
	}

	return "状态未知"
}

// parseTrackTime 解析轨迹时间
func (a *KuaidiNiaoAdapter) parseTrackTime(timeStr string) (time.Time, error) {
	// 🔥 修复：优先使用北京时区解析无时区信息的时间字符串
	beijingLocation, _ := time.LoadLocation("Asia/Shanghai")

	// 常见时间格式列表 - 优先处理无时区信息的格式
	formatsWithLocation := []string{
		"2006-01-02 15:04:05",
		"2006/01/02 15:04:05",
	}

	// 先尝试使用北京时区解析无时区信息的格式
	for _, format := range formatsWithLocation {
		if t, err := time.ParseInLocation(format, timeStr, beijingLocation); err == nil {
			return t, nil
		}
	}

	// 然后尝试带时区信息的格式
	formatsWithTimezone := []string{
		"Mon Jan 02 15:04:05 MST 2006", // Thu Mar 30 05:37:57 CST 2023
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05.000Z",
		"2006-01-02T15:04:05+08:00",
		"2006-01-02T15:04:05-07:00",
	}

	for _, format := range formatsWithTimezone {
		if t, err := time.Parse(format, timeStr); err == nil {
			// 转换为北京时间
			return util.ToBeijing(t), nil
		}
	}

	return time.Time{}, fmt.Errorf("无法解析时间格式: %s", timeStr)
}

// ==== 智能缓存相关方法 ====

// generateRouteKey 生成路线缓存键
func (a *KuaidiNiaoAdapter) generateRouteKey(req *model.PriceRequest) string {
	return fmt.Sprintf("%s->%s",
		req.Sender.Province+req.Sender.City,
		req.Receiver.Province+req.Receiver.City)
}

// generateRequestHash 生成请求参数hash
func (a *KuaidiNiaoAdapter) generateRequestHash(req *model.PriceRequest) string {
	data := fmt.Sprintf("%s|%s|%.2f|%.2f|%.2f|%.2f|%s",
		req.Sender.Province+req.Sender.City+req.Sender.District,
		req.Receiver.Province+req.Receiver.City+req.Receiver.District,
		req.Package.Weight,
		req.Package.Length,
		req.Package.Width,
		req.Package.Height,
		req.Package.GoodsName)

	hash := md5.Sum([]byte(data))
	return fmt.Sprintf("%x", hash)
}

// getCachedPrices 从缓存获取价格数据
func (a *KuaidiNiaoAdapter) getCachedPrices(routeKey, requestHash string) *KuaidiNiaoCachedPrices {
	a.cacheMutex.RLock()
	defer a.cacheMutex.RUnlock()

	cached, exists := a.priceCache[routeKey]
	if !exists {
		return nil
	}

	// 检查缓存是否过期
	if time.Since(cached.CachedAt) > a.cacheExpiry {
		return nil
	}

	// 检查请求参数是否匹配
	if cached.RequestHash != requestHash {
		return nil
	}

	return cached
}

// cachePrices 缓存价格数据
func (a *KuaidiNiaoAdapter) cachePrices(routeKey, requestHash string, prices []model.StandardizedPrice) {
	a.cacheMutex.Lock()
	defer a.cacheMutex.Unlock()

	a.priceCache[routeKey] = &KuaidiNiaoCachedPrices{
		Route:       routeKey,
		Prices:      prices,
		CachedAt:    time.Now(),
		RequestHash: requestHash,
	}

	a.lastCacheTime[routeKey] = time.Now()
}

// callBatchPriceAPI 调用快递鸟批量查价API
func (a *KuaidiNiaoAdapter) callBatchPriceAPI(ctx context.Context, req *model.PriceRequest) ([]model.StandardizedPrice, error) {
	// 1. 转换为快递鸟API格式
	a.logger.Info("🔄 开始转换为快递鸟API格式")
	apiReq := a.convertToPriceAPIRequest(req)

	// 2. 调用快递鸟API
	a.logger.Info("🌐 开始调用快递鸟API")
	apiResp, err := a.callPriceAPIWithRetry(ctx, apiReq)
	if err != nil {
		return nil, fmt.Errorf("调用价格查询API失败: %w", err)
	}

	// 🔍 详细调试：记录API响应
	a.logger.Info("📥 快递鸟API响应",
		zap.Bool("success", apiResp.Success),
		zap.String("reason", apiResp.Reason),
		zap.String("result_code", apiResp.ResultCode),
		zap.Int("price_data_count", len(apiResp.Data)))

	// 记录每个价格数据的详细信息
	for i, priceData := range apiResp.Data {
		a.logger.Info("💰 快递鸟原始价格数据",
			zap.Int("index", i),
			zap.String("shipper_code", priceData.ShipperCode),
			zap.String("weight", priceData.Weight),
			zap.String("cost", priceData.Cost),
			zap.Float64("first_weight", priceData.FirstWeight),
			zap.Float64("first_weight_amount", priceData.FirstWeightAmount),
			zap.Float64("continuous_weight", priceData.ContinuousWeight),
			zap.Float64("continuous_weight_price", priceData.ContinuousWeightPrice),
			zap.Float64("continuous_weight_amount", priceData.ContinuousWeightAmount),
			zap.Float64("total_fee", priceData.TotalFee))
	}

	// 3. 转换为标准格式
	a.logger.Info("🔄 开始转换为标准价格格式")
	standardPrices := a.convertToStandardizedPrices(apiResp, req)

	// 🔍 详细调试：记录转换后的标准价格
	for i, price := range standardPrices {
		a.logger.Info("🏷️ 转换后的标准价格数据",
			zap.Int("index", i),
			zap.String("express_code", price.ExpressCode),
			zap.String("express_name", price.ExpressName),
			zap.String("product_code", price.ProductCode),
			zap.String("product_name", price.ProductName),
			zap.Float64("price", price.Price),
			zap.Float64("continued_weight_per_kg", price.ContinuedWeightPerKg),
			zap.Float64("calc_weight", price.CalcWeight),
			zap.String("order_code", price.OrderCode),
			zap.String("provider", price.Provider),
			zap.String("channel_id", price.ChannelID))
	}

	a.logger.Info("🎉 快递鸟批量价格查询成功完成",
		zap.String("provider", "kuaidiniao"),
		zap.Int("total_price_count", len(standardPrices)))

	return standardPrices, nil
}

// filterPricesByExpressType 根据快递公司类型过滤价格
func (a *KuaidiNiaoAdapter) filterPricesByExpressType(allPrices []model.StandardizedPrice, expressType string) ([]model.StandardizedPrice, error) {
	if expressType == "" {
		// 未指定快递公司：按数据库映射过滤，仅返回 is_supported=true 的公司
		ctx := context.Background()
		if a.mappingService == nil {
			a.logger.Error("❌ 映射服务未初始化，禁止返回快递鸟价格（未指定快递公司）")
			return []model.StandardizedPrice{}, nil
		}
		type supportedCompaniesFetcher interface {
			GetSupportedCompanies(ctx context.Context, providerCode string) ([]*express.ExpressMappingCache, error)
			GetProviderSupportedCompaniesNoCache(ctx context.Context, providerCode string) ([]*express.ExpressMappingCache, error) // 🔥 新增无缓存方法
		}
		ms, ok := a.mappingService.(supportedCompaniesFetcher)
		if !ok {
			a.logger.Error("❌ 映射服务类型断言失败，禁止返回快递鸟价格（未指定快递公司）")
			return []model.StandardizedPrice{}, nil
		}
		// 🔥 关键修复：使用无缓存查询，确保状态变更立即生效
		companies, err := ms.GetProviderSupportedCompaniesNoCache(ctx, "kuaidiniao")
		if err != nil {
			a.logger.Error("❌ 无缓存获取供应商支持的快递公司失败，禁止返回快递鸟价格（未指定快递公司）", zap.Error(err))
			return []model.StandardizedPrice{}, nil
		}
		// 构建允许的供应商快递代码集合（ProviderCompanyCode）
		allowed := make(map[string]bool, len(companies))
		for _, m := range companies {
			if m != nil && m.IsSupported {
				allowed[m.ProviderCompanyCode] = true
			}
		}
		filtered := make([]model.StandardizedPrice, 0, len(allPrices))
		skipped := []string{}
		for _, price := range allPrices {
			if !allowed[price.ExpressCode] {
				skipped = append(skipped, price.ExpressCode)
				continue
			}
			filtered = append(filtered, price)
		}
		a.logger.Info("🎯 未指定快递公司，按映射过滤完成",
			zap.Int("input_total", len(allPrices)),
			zap.Int("output_total", len(filtered)),
			zap.Strings("skipped_companies", skipped))
		return filtered, nil
	}

	// 🔧 修复：获取快递鸟供应商的快递公司代码映射
	ctx := context.Background()
	var targetExpressCode string

	// 尝试通过映射服务获取快递鸟的快递公司代码（强制依赖数据库配置，禁止任何回退）
	if a.mappingService != nil {
		if mappingService, ok := a.mappingService.(interface {
			GetProviderCompanyCode(ctx context.Context, companyCode, providerCode string) (string, error)
		}); ok {
			mappedCode, err := mappingService.GetProviderCompanyCode(ctx, expressType, "kuaidiniao")
			if err != nil {
				// 严格遵循“数据库唯一真相”，禁止使用原始/静态映射作为回退
				a.logger.Error("❌ 快递公司代码映射失败（禁用/未配置），禁止继续",
					zap.String("express_type", expressType),
					zap.Error(err))
				return []model.StandardizedPrice{}, nil
			}
			targetExpressCode = mappedCode
			a.logger.Info("🔄 使用映射后的快递公司代码",
				zap.String("standard_code", expressType),
				zap.String("provider_code", targetExpressCode))
		} else {
			// 类型不匹配视为严重配置问题，直接返回空结果
			a.logger.Error("❌ 映射服务类型断言失败（系统配置错误），禁止继续",
				zap.String("express_type", expressType))
			return []model.StandardizedPrice{}, nil
		}
	} else {
		// 未注入映射服务属于系统配置错误，为避免越权调用，直接返回空
		a.logger.Error("❌ 映射服务未初始化，禁止进行快递代码转换",
			zap.String("express_type", expressType))
		return []model.StandardizedPrice{}, nil
	}

	var filteredPrices []model.StandardizedPrice
	for _, price := range allPrices {
		// 🔧 修复：同时匹配原始代码和映射后的代码
		if price.ExpressCode == expressType || price.ExpressCode == targetExpressCode {
			filteredPrices = append(filteredPrices, price)
			a.logger.Info("✅ 找到匹配的价格",
				zap.String("requested_code", expressType),
				zap.String("target_code", targetExpressCode),
				zap.String("actual_code", price.ExpressCode),
				zap.String("express_name", price.ExpressName),
				zap.Float64("price", price.Price))
		}
	}

	if len(filteredPrices) == 0 {
		a.logger.Warn("⚠️ 未找到指定快递公司的价格",
			zap.String("requested_express_type", expressType),
			zap.String("target_express_code", targetExpressCode),
			zap.Int("available_prices", len(allPrices)))

		// 记录可用的快递公司
		availableCompanies := make([]string, 0, len(allPrices))
		for _, price := range allPrices {
			availableCompanies = append(availableCompanies, price.ExpressCode)
		}
		a.logger.Info("📋 可用的快递公司", zap.Strings("companies", availableCompanies))

		return []model.StandardizedPrice{}, nil
	}

	a.logger.Info("🎯 价格过滤成功",
		zap.String("express_type", expressType),
		zap.String("target_code", targetExpressCode),
		zap.Int("filtered_count", len(filteredPrices)))

	return filteredPrices, nil
}

// ==== 订单信息查询相关方法 ====

// callOrderQueryAPIWithRetry 调用订单信息查询API（带重试）
func (a *KuaidiNiaoAdapter) callOrderQueryAPIWithRetry(ctx context.Context, orderNo string) (*model.OrderInfo, error) {
	var lastErr error
	maxRetries := 3

	for i := 0; i < maxRetries; i++ {
		if i > 0 {
			// 重试前等待
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(time.Duration(i) * time.Second):
			}
		}

		orderInfo, err := a.callOrderQueryAPI(ctx, orderNo)
		if err == nil {
			return orderInfo, nil
		}

		lastErr = err
		a.logger.Warn("快递鸟订单信息查询失败，准备重试",
			zap.String("order_no", orderNo),
			zap.Int("attempt", i+1),
			zap.Int("max_retries", maxRetries),
			zap.Error(err))
	}

	return nil, fmt.Errorf("订单信息查询失败，已重试%d次: %w", maxRetries, lastErr)
}

// callOrderQueryAPI 调用订单信息查询API（接口指令1804）
func (a *KuaidiNiaoAdapter) callOrderQueryAPI(ctx context.Context, orderNo string) (*model.OrderInfo, error) {
	// 构建查询请求
	queryReq := &KuaidiNiaoOrderQueryRequest{
		OrderCode: orderNo,
		// ShipperCode 可选，不设置表示查询所有快递公司
	}

	// 序列化请求数据
	requestData, err := json.Marshal(queryReq)
	if err != nil {
		return nil, fmt.Errorf("序列化请求数据失败: %w", err)
	}

	// 构建系统级参数
	systemParams := KuaidiNiaoSystemParams{
		RequestType: "1804", // 订单信息查询接口指令
		EBusinessID: a.config.EBusinessID,
		RequestData: string(requestData),
		DataType:    "2",
		DataSign:    a.generateSignature(string(requestData)),
	}

	// 调用API
	respData, err := a.callAPI(ctx, systemParams)
	if err != nil {
		return nil, err
	}

	// 解析响应
	var apiResp KuaidiNiaoOrderQueryResponse
	if err := json.Unmarshal(respData, &apiResp); err != nil {
		return nil, fmt.Errorf("解析订单信息查询响应失败: %w", err)
	}

	// 检查业务层面的错误
	if !apiResp.Success {
		return nil, fmt.Errorf("快递鸟订单信息查询失败 [%s]: %s", apiResp.ResultCode, apiResp.Reason)
	}

	// 转换为标准格式
	orderInfo := a.convertToOrderInfo(&apiResp)

	return orderInfo, nil
}

// convertToOrderInfo 转换为标准订单信息格式
func (a *KuaidiNiaoAdapter) convertToOrderInfo(apiResp *KuaidiNiaoOrderQueryResponse) *model.OrderInfo {
	// 解析创建时间
	var createdAt time.Time
	if apiResp.CreateTime != "" {
		if t, err := time.Parse("2006-01-02 15:04:05", apiResp.CreateTime); err == nil {
			createdAt = t
		}
	}

	return &model.OrderInfo{
		OrderNo:        apiResp.OrderCode,
		TrackingNo:     apiResp.LogisticCode,
		ExpressType:    apiResp.ShipperCode,
		Status:         apiResp.State,
		StatusDesc:     a.getOrderStatusDescription(apiResp.State),
		Weight:         apiResp.Weight,
		Price:          apiResp.TotalFee, // 使用总费用作为价格
		CreatedAt:      createdAt,
		CourierName:    apiResp.PersonName,
		CourierPhone:   apiResp.PersonTel,
		CourierCode:    apiResp.PersonCode,
		StationName:    apiResp.StationName,
		StationCode:    apiResp.StationCode,
		StationAddress: apiResp.StationAddress,
		StationPhone:   apiResp.StationTel,
		PickupCode:     apiResp.PickupCode,
	}
}

// getOrderStatusDescription 获取订单状态描述（用于订单查询接口）
func (a *KuaidiNiaoAdapter) getOrderStatusDescription(state string) string {
	statusMap := map[string]string{
		"99":  "下单失败",
		"100": "下单成功",
		"102": "已接单",
		"103": "已分配",
		"104": "已取件",
		"301": "已揽件",
		"203": "已取消",
		"2":   "在途中",
		"3":   "已签收",
	}

	if desc, exists := statusMap[state]; exists {
		return desc
	}
	return fmt.Sprintf("未知状态(%s)", state)
}

// ==== 快递鸟取消状态确认相关方法 ====

// verifyCancellationStatus 验证取消状态 - 主动查询确认机制
// 🔥 解决快递鸟不推送已取消状态的问题
func (a *KuaidiNiaoAdapter) verifyCancellationStatus(ctx context.Context, orderNo string) error {
	a.logger.Info("🔍 [主动查询] 开始验证快递鸟订单取消状态",
		zap.String("order_no", orderNo),
		zap.String("verify_step", "1_start"))

	// 1. 等待一段时间让快递鸟系统处理取消请求
	waitTime := 3 * time.Second
	a.logger.Info("⏰ [主动查询] 等待快递鸟系统处理取消请求",
		zap.String("order_no", orderNo),
		zap.Duration("wait_time", waitTime),
		zap.String("verify_step", "2_wait"))

	select {
	case <-ctx.Done():
		a.logger.Error("❌ [主动查询] 上下文取消", zap.String("order_no", orderNo), zap.Error(ctx.Err()))
		return ctx.Err()
	case <-time.After(waitTime):
		a.logger.Info("✅ [主动查询] 等待完成，开始查询", zap.String("order_no", orderNo))
	}

	// 2. 查询订单状态，增加重试次数和智能间隔
	maxRetries := 10                 // 🔥 增加重试次数
	retryInterval := 3 * time.Second // 🔥 增加重试间隔

	a.logger.Info("🔄 [主动查询] 开始重试查询循环",
		zap.String("order_no", orderNo),
		zap.Int("max_retries", maxRetries),
		zap.Duration("retry_interval", retryInterval),
		zap.String("verify_step", "3_retry_loop"))

	for i := 0; i < maxRetries; i++ {
		currentAttempt := i + 1
		a.logger.Info("🎯 [主动查询] 开始第 %d 次查询尝试",
			zap.String("order_no", orderNo),
			zap.Int("current_attempt", currentAttempt),
			zap.Int("max_retries", maxRetries),
			zap.String("verify_step", fmt.Sprintf("4_attempt_%d", currentAttempt)))

		if i > 0 {
			a.logger.Info("⏳ [主动查询] 等待重试间隔",
				zap.String("order_no", orderNo),
				zap.Int("attempt", currentAttempt),
				zap.Duration("retry_interval", retryInterval))

			select {
			case <-ctx.Done():
				a.logger.Error("❌ [主动查询] 重试等待期间上下文取消", zap.String("order_no", orderNo), zap.Error(ctx.Err()))
				return ctx.Err()
			case <-time.After(retryInterval):
				a.logger.Info("✅ [主动查询] 重试等待完成", zap.String("order_no", orderNo))
			}
		}

		// 查询订单状态
		a.logger.Info("🌐 [主动查询] 调用快递鸟订单查询API",
			zap.String("order_no", orderNo),
			zap.Int("attempt", currentAttempt))

		orderInfo, err := a.callOrderQueryAPI(ctx, orderNo)
		if err != nil {
			a.logger.Warn("⚠️ [主动查询] 查询订单状态失败，准备重试",
				zap.String("order_no", orderNo),
				zap.Int("attempt", currentAttempt),
				zap.Error(err),
				zap.String("verify_step", fmt.Sprintf("4_attempt_%d_failed", currentAttempt)))

			// 🔥 增强：智能重试间隔（指数退避）
			if currentAttempt < maxRetries {
				smartRetryInterval := time.Duration(currentAttempt) * retryInterval
				a.logger.Info("⏳ [主动查询] 智能重试等待",
					zap.String("order_no", orderNo),
					zap.Int("attempt", currentAttempt),
					zap.Duration("smart_retry_interval", smartRetryInterval))

				select {
				case <-ctx.Done():
					a.logger.Error("❌ [主动查询] 智能重试等待期间上下文取消", zap.String("order_no", orderNo), zap.Error(ctx.Err()))
					return ctx.Err()
				case <-time.After(smartRetryInterval):
					// 继续下一次重试
				}
			}
			continue
		}

		a.logger.Info("📋 [主动查询] 查询到订单状态",
			zap.String("order_no", orderNo),
			zap.String("status", orderInfo.Status),
			zap.String("status_desc", orderInfo.StatusDesc),
			zap.String("tracking_no", orderInfo.TrackingNo),
			zap.String("express_type", orderInfo.ExpressType),
			zap.Int("attempt", currentAttempt),
			zap.String("verify_step", fmt.Sprintf("4_attempt_%d_success", currentAttempt)))

		// 3. 检查是否已取消
		if orderInfo.Status == "203" { // 203 = 已取消
			a.logger.Info("🎉 [主动查询] 确认订单已取消，准备触发退款逻辑",
				zap.String("order_no", orderNo),
				zap.String("status", orderInfo.Status),
				zap.String("status_desc", orderInfo.StatusDesc),
				zap.Int("attempt", currentAttempt),
				zap.String("verify_step", "5_confirmed_cancelled"))

			// 🔥 关键：触发取消确认回调，执行退款逻辑
			a.logger.Info("💰 [主动查询] 开始触发取消确认回调", zap.String("order_no", orderNo))
			err = a.triggerCancellationCallback(ctx, orderNo, orderInfo)
			if err != nil {
				a.logger.Error("❌ [主动查询] 触发取消确认回调失败",
					zap.String("order_no", orderNo),
					zap.Error(err),
					zap.String("verify_step", "5_callback_failed"))
				return fmt.Errorf("触发取消确认回调失败: %w", err)
			}

			a.logger.Info("✅ [主动查询] 取消确认流程完成",
				zap.String("order_no", orderNo),
				zap.String("verify_step", "6_complete"))
			return nil
		}

		// 🔥 修复：检查是否为下单失败状态，如果是则直接触发取消确认回调
		if orderInfo.Status == "99" { // 99 = 下单失败
			a.logger.Info("🎉 [主动查询] 确认订单下单失败，视为取消成功，准备触发退款逻辑",
				zap.String("order_no", orderNo),
				zap.String("status", orderInfo.Status),
				zap.String("status_desc", orderInfo.StatusDesc),
				zap.Int("attempt", currentAttempt),
				zap.String("verify_step", "5_confirmed_submit_failed"))

			// 🔥 关键：触发取消确认回调，执行退款逻辑
			a.logger.Info("💰 [主动查询] 下单失败订单开始触发取消确认回调", zap.String("order_no", orderNo))
			err = a.triggerCancellationCallback(ctx, orderNo, orderInfo)
			if err != nil {
				a.logger.Error("❌ [主动查询] 触发取消确认回调失败",
					zap.String("order_no", orderNo),
					zap.Error(err),
					zap.String("verify_step", "5_callback_failed"))
				return fmt.Errorf("触发取消确认回调失败: %w", err)
			}

			a.logger.Info("✅ [主动查询] 下单失败订单取消确认流程完成",
				zap.String("order_no", orderNo),
				zap.String("verify_step", "6_complete"))
			return nil
		}

		// 4. 检查是否为其他终态（不可取消的状态）
		if a.isFinalStatus(orderInfo.Status) {
			a.logger.Error("❌ [主动查询] 订单为终态，无法取消",
				zap.String("order_no", orderNo),
				zap.String("status", orderInfo.Status),
				zap.String("status_desc", orderInfo.StatusDesc),
				zap.String("verify_step", "4_final_status"))
			return fmt.Errorf("订单状态为 %s (%s)，无法取消", orderInfo.Status, orderInfo.StatusDesc)
		}

		// 5. 如果还在处理中，继续等待
		a.logger.Info("⏳ [主动查询] 订单仍在处理中，继续等待下次重试",
			zap.String("order_no", orderNo),
			zap.String("status", orderInfo.Status),
			zap.String("status_desc", orderInfo.StatusDesc),
			zap.Int("attempt", currentAttempt),
			zap.Int("remaining_attempts", maxRetries-currentAttempt),
			zap.String("verify_step", fmt.Sprintf("4_attempt_%d_pending", currentAttempt)))
	}

	// 6. 超过重试次数仍未确认取消
	a.logger.Error("❌ [主动查询] 超过最大重试次数仍未确认订单取消状态",
		zap.String("order_no", orderNo),
		zap.Int("max_retries", maxRetries),
		zap.String("verify_step", "7_timeout"))
	return fmt.Errorf("超过最大重试次数(%d)仍未确认订单取消状态", maxRetries)
}

// triggerCancellationCallback 触发取消确认回调
func (a *KuaidiNiaoAdapter) triggerCancellationCallback(ctx context.Context, orderNo string, orderInfo *model.OrderInfo) error {
	a.logger.Info("📤 [回调触发] 开始触发快递鸟取消确认回调",
		zap.String("order_no", orderNo),
		zap.String("status", orderInfo.Status),
		zap.String("tracking_no", orderInfo.TrackingNo),
		zap.String("express_type", orderInfo.ExpressType),
		zap.String("callback_step", "1_start"))

	// 🔥 关键：构建标准化的快递鸟取消回调数据，模拟正常的回调格式
	// 这样可以复用现有的回调处理逻辑
	currentTime := time.Now().Format("2006-01-02 15:04:05")
	callbackData := map[string]interface{}{
		"PushTime":    currentTime,
		"EBusinessID": a.config.EBusinessID,
		"Data": []map[string]interface{}{
			{
				"ShipperCode":     orderInfo.ExpressType,
				"CreateTime":      currentTime,
				"OrderCode":       orderNo,
				"EBusinessID":     a.config.EBusinessID,
				"Success":         true,
				"CallRequestType": "1801",
				"Reason":          "主动查询确认已取消",
				"LogisticCode":    orderInfo.TrackingNo,
				"State":           "203", // 已取消
				"KDNOrderCode":    orderInfo.TrackingNo,
				"OperateType":     1,              // 快递鸟操作
				"ConfirmedBy":     "active_query", // 标识这是主动查询确认的
			},
		},
		"Count": 1,
	}

	a.logger.Info("📋 [回调触发] 构建回调数据完成",
		zap.String("order_no", orderNo),
		zap.String("push_time", currentTime),
		zap.String("e_business_id", a.config.EBusinessID),
		zap.String("shipper_code", orderInfo.ExpressType),
		zap.String("logistic_code", orderInfo.TrackingNo),
		zap.String("state", "203"),
		zap.String("callback_step", "2_data_built"))

	// 🔥 方案：发送内部HTTP回调到本地回调接口
	// 这样可以复用现有的回调处理逻辑，包括退款逻辑
	a.logger.Info("🌐 [回调触发] 开始发送内部HTTP回调",
		zap.String("order_no", orderNo),
		zap.String("callback_step", "3_send_start"))

	err := a.sendInternalCallback(ctx, callbackData)
	if err != nil {
		a.logger.Error("❌ [回调触发] 发送内部回调失败",
			zap.String("order_no", orderNo),
			zap.Error(err),
			zap.String("callback_step", "3_send_failed"))
		return fmt.Errorf("发送内部回调失败: %w", err)
	}

	a.logger.Info("✅ [回调触发] 快递鸟取消确认回调已成功发送",
		zap.String("order_no", orderNo),
		zap.String("action", "internal_callback_sent"),
		zap.String("callback_step", "4_success"))

	return nil
}

// sendInternalCallback 发送内部回调
func (a *KuaidiNiaoAdapter) sendInternalCallback(ctx context.Context, callbackData map[string]interface{}) error {
	a.logger.Info("🔄 [内部回调] 开始发送内部回调流程", zap.String("send_step", "1_start"))

	// 1. 序列化回调数据
	a.logger.Info("📦 [内部回调] 序列化回调数据", zap.String("send_step", "2_serialize"))
	jsonData, err := json.Marshal(callbackData)
	if err != nil {
		a.logger.Error("❌ [内部回调] 序列化回调数据失败", zap.Error(err), zap.String("send_step", "2_serialize_failed"))
		return fmt.Errorf("序列化回调数据失败: %w", err)
	}

	a.logger.Info("✅ [内部回调] 回调数据序列化成功",
		zap.Int("json_size", len(jsonData)),
		zap.String("json_preview", string(jsonData)[:min(200, len(jsonData))]),
		zap.String("send_step", "2_serialize_success"))

	// 2. URL编码
	a.logger.Info("🔗 [内部回调] URL编码回调数据", zap.String("send_step", "3_encode"))
	encodedData := url.QueryEscape(string(jsonData))
	formData := fmt.Sprintf("RequestData=%s&DataSign=INTERNAL_CALLBACK&RequestType=103", encodedData)

	a.logger.Info("✅ [内部回调] URL编码完成",
		zap.Int("form_data_size", len(formData)),
		zap.String("send_step", "3_encode_success"))

	// 3. 发送到本地回调接口
	callbackURL := "http://localhost:8081/api/v1/callbacks/kuaidiniao"
	a.logger.Info("🌐 [内部回调] 准备发送HTTP请求",
		zap.String("callback_url", callbackURL),
		zap.String("method", "POST"),
		zap.String("send_step", "4_prepare_request"))

	req, err := http.NewRequestWithContext(ctx, "POST", callbackURL, strings.NewReader(formData))
	if err != nil {
		a.logger.Error("❌ [内部回调] 创建HTTP请求失败", zap.Error(err), zap.String("send_step", "4_request_failed"))
		return fmt.Errorf("创建回调请求失败: %w", err)
	}

	req.Header.Set("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8")
	req.Header.Set("User-Agent", "KuaidiNiao-Internal-Callback/1.0")
	req.Header.Set("X-Internal-Callback", "true") // 标识这是内部回调

	a.logger.Info("📋 [内部回调] HTTP请求头设置完成",
		zap.String("content_type", req.Header.Get("Content-Type")),
		zap.String("user_agent", req.Header.Get("User-Agent")),
		zap.String("internal_callback", req.Header.Get("X-Internal-Callback")),
		zap.String("send_step", "4_headers_set"))

	// 4. 发送请求
	a.logger.Info("📤 [内部回调] 发送HTTP请求", zap.String("send_step", "5_send"))
	start := time.Now()
	resp, err := a.client.Do(req)
	duration := time.Since(start)

	if err != nil {
		a.logger.Error("❌ [内部回调] 发送HTTP请求失败",
			zap.Error(err),
			zap.Duration("duration", duration),
			zap.String("send_step", "5_send_failed"))
		return fmt.Errorf("发送回调请求失败: %w", err)
	}
	defer resp.Body.Close()

	a.logger.Info("📥 [内部回调] 收到HTTP响应",
		zap.Int("status_code", resp.StatusCode),
		zap.Duration("duration", duration),
		zap.String("send_step", "5_response_received"))

	// 5. 检查响应
	body, readErr := io.ReadAll(resp.Body)
	if readErr != nil {
		a.logger.Warn("⚠️ [内部回调] 读取响应体失败", zap.Error(readErr))
	}

	a.logger.Info("📄 [内部回调] 响应详情",
		zap.Int("status_code", resp.StatusCode),
		zap.String("response_body", string(body)),
		zap.String("send_step", "6_response_details"))

	if resp.StatusCode != http.StatusOK {
		a.logger.Error("❌ [内部回调] 回调请求失败",
			zap.Int("status_code", resp.StatusCode),
			zap.String("response_body", string(body)),
			zap.String("send_step", "6_response_failed"))
		return fmt.Errorf("回调请求失败，状态码: %d, 响应: %s", resp.StatusCode, string(body))
	}

	a.logger.Info("🎉 [内部回调] 内部回调发送成功",
		zap.String("callback_url", callbackURL),
		zap.Int("status_code", resp.StatusCode),
		zap.Duration("total_duration", duration),
		zap.String("send_step", "7_success"))

	return nil
}

// isFinalStatus 检查是否为终态状态
func (a *KuaidiNiaoAdapter) isFinalStatus(status string) bool {
	finalStatuses := []string{
		"3",   // 已签收
		"203", // 已取消
		// 🔥 修复：移除状态99（下单失败），允许下单失败的订单被取消并退款
		// "99",  // 下单失败 - 移除，因为下单失败的订单应该允许取消并退款
	}

	for _, finalStatus := range finalStatuses {
		if status == finalStatus {
			return true
		}
	}
	return false
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// VerifyCancellationStatusAsync 异步验证取消状态 - 用于处理已经是cancelling状态的订单
// 🔥 解决快递鸟订单卡在cancelling状态的问题
func (a *KuaidiNiaoAdapter) VerifyCancellationStatusAsync(ctx context.Context, orderNo string) error {
	a.logger.Info("🔄 [异步主动查询] 开始异步验证快递鸟订单取消状态",
		zap.String("order_no", orderNo),
		zap.String("async_step", "1_start"))

	// 直接调用现有的主动查询确认机制
	return a.verifyCancellationStatus(ctx, orderNo)
}

// 🔥 新增：实现缓存失效接口，支持热重启
// InvalidateCache 失效指定路线模式的缓存
func (a *KuaidiNiaoAdapter) InvalidateCache(routePattern string) error {
	a.cacheMutex.Lock()
	defer a.cacheMutex.Unlock()

	deletedCount := 0
	for routeKey := range a.priceCache {
		// 如果routePattern为空，清理所有缓存
		// 否则只清理匹配模式的缓存
		if routePattern == "" || strings.Contains(routeKey, routePattern) {
			delete(a.priceCache, routeKey)
			delete(a.lastCacheTime, routeKey)
			deletedCount++
		}
	}

	a.logger.Info("快递鸟缓存失效完成",
		zap.String("route_pattern", routePattern),
		zap.Int("deleted_count", deletedCount))

	return nil
}

// InvalidateAllCache 清理所有缓存
func (a *KuaidiNiaoAdapter) InvalidateAllCache() error {
	return a.InvalidateCache("")
}
