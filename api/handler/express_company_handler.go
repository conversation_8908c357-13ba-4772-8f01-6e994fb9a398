package handler

import (
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/your-org/go-kuaidi/internal/adapter"
	"github.com/your-org/go-kuaidi/internal/errors"
	"github.com/your-org/go-kuaidi/internal/express"
	"go.uber.org/zap"
)

// StatusUpdateRequest 状态更新请求
type StatusUpdateRequest struct {
	Enabled *bool  `json:"enabled" binding:"required"`          // 是否启用（使用指针避免false值验证问题）
	Reason  string `json:"reason,omitempty" validate:"max=200"` // 变更原因
}

// BatchStatusUpdateRequest 批量状态更新请求
type BatchStatusUpdateRequest struct {
	Companies []string              `json:"companies,omitempty"` // 快递公司代码列表
	Providers []string              `json:"providers,omitempty"` // 供应商代码列表
	Mappings  []MappingStatusUpdate `json:"mappings,omitempty"`  // 映射关系列表
	Enabled   bool                  `json:"enabled"`             // 目标状态
	Reason    string                `json:"reason,omitempty"`    // 变更原因
}

// MappingStatusUpdate 映射关系状态更新
type MappingStatusUpdate struct {
	CompanyCode  string `json:"company_code" binding:"required"`  // 快递公司代码
	ProviderCode string `json:"provider_code" binding:"required"` // 供应商代码
}

// BatchUpdateResult 批量更新结果
type BatchUpdateResult struct {
	SuccessCount int                     `json:"success_count"` // 成功数量
	FailedCount  int                     `json:"failed_count"`  // 失败数量
	Results      []BatchUpdateItemResult `json:"results"`       // 详细结果
}

// BatchUpdateItemResult 批量更新项目结果
type BatchUpdateItemResult struct {
	Type         string `json:"type"`                    // 类型：company/provider/mapping
	Code         string `json:"code"`                    // 代码
	CompanyCode  string `json:"company_code,omitempty"`  // 快递公司代码（映射关系用）
	ProviderCode string `json:"provider_code,omitempty"` // 供应商代码（映射关系用）
	Success      bool   `json:"success"`                 // 是否成功
	Error        string `json:"error,omitempty"`         // 错误信息
}

// ExpressCompanyHandler 快递公司管理处理器
type ExpressCompanyHandler struct {
	service                express.ExpressCompanyService
	logger                 *zap.Logger
	dynamicProviderManager *adapter.DynamicProviderManager
}

// NewExpressCompanyHandler 创建快递公司管理处理器
func NewExpressCompanyHandler(
	service express.ExpressCompanyService,
	logger *zap.Logger,
	dynamicProviderManager *adapter.DynamicProviderManager,
) *ExpressCompanyHandler {
	return &ExpressCompanyHandler{
		service:                service,
		logger:                 logger,
		dynamicProviderManager: dynamicProviderManager,
	}
}

// CreateCompany 创建快递公司
// @Summary 创建快递公司
// @Description 管理员创建新的快递公司
// @Tags 快递公司管理
// @Accept json
// @Produce json
// @Param request body express.CreateCompanyRequest true "创建快递公司请求"
// @Success 200 {object} express.ExpressCompany "创建成功"
// @Failure 400 {object} errors.ErrorResponse "请求参数错误"
// @Failure 401 {object} errors.ErrorResponse "未授权"
// @Failure 403 {object} errors.ErrorResponse "权限不足"
// @Failure 409 {object} errors.ErrorResponse "快递公司代码已存在"
// @Failure 500 {object} errors.ErrorResponse "服务器内部错误"
// @Router /api/v1/admin/express/companies [post]
// @Security BearerAuth
func (h *ExpressCompanyHandler) CreateCompany(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		h.logger.Warn("Admin user ID not found in context", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	operatorIDStr, ok := operatorID.(string)
	if !ok || operatorIDStr == "" {
		h.logger.Warn("Invalid admin user ID in context", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "无效的用户ID")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 解析请求参数
	var req express.CreateCompanyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request parameters",
			zap.String("request_id", requestID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效: "+err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 调用服务层
	company, err := h.service.CreateCompany(c.Request.Context(), req, operatorIDStr)
	if err != nil {
		h.logger.Error("Failed to create express company",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorIDStr),
			zap.Any("request", req),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	h.logger.Info("Express company created successfully",
		zap.String("request_id", requestID),
		zap.String("company_id", company.ID),
		zap.String("code", company.Code),
		zap.String("operator_id", operatorIDStr))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "快递公司创建成功",
		"data":    company,
	})
}

// GetCompany 获取快递公司详情
// @Summary 获取快递公司详情
// @Description 根据ID获取快递公司详细信息
// @Tags 快递公司管理
// @Accept json
// @Produce json
// @Param id path string true "快递公司ID"
// @Success 200 {object} express.ExpressCompany "获取成功"
// @Failure 400 {object} errors.ErrorResponse "请求参数错误"
// @Failure 401 {object} errors.ErrorResponse "未授权"
// @Failure 404 {object} errors.ErrorResponse "快递公司不存在"
// @Failure 500 {object} errors.ErrorResponse "服务器内部错误"
// @Router /api/v1/admin/express/companies/{id} [get]
// @Security BearerAuth
func (h *ExpressCompanyHandler) GetCompany(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取路径参数
	companyID := c.Param("id")
	if companyID == "" {
		h.logger.Warn("Company ID is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "快递公司ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 调用服务层
	company, err := h.service.GetCompanyByID(c.Request.Context(), companyID)
	if err != nil {
		h.logger.Error("Failed to get express company",
			zap.String("request_id", requestID),
			zap.String("company_id", companyID),
			zap.Error(err))

		if err.Error() == "快递公司不存在" {
			businessErr := errors.NewBusinessError(errors.ErrCodeNotFound, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    company,
	})
}

// GetCompanies 获取快递公司列表
// @Summary 获取快递公司列表
// @Description 分页获取快递公司列表，支持搜索和过滤
// @Tags 快递公司管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param page_size query int false "每页数量" default(20)
// @Param keyword query string false "搜索关键词"
// @Param is_active query bool false "是否启用"
// @Param sort_by query string false "排序字段"
// @Param sort_order query string false "排序方向" Enums(ASC, DESC)
// @Success 200 {object} express.CompanyListResult "获取成功"
// @Failure 400 {object} errors.ErrorResponse "请求参数错误"
// @Failure 401 {object} errors.ErrorResponse "未授权"
// @Failure 500 {object} errors.ErrorResponse "服务器内部错误"
// @Router /api/v1/admin/express/companies [get]
// @Security BearerAuth
func (h *ExpressCompanyHandler) GetCompanies(c *gin.Context) {
	requestID := getRequestID(c)

	// 解析查询参数
	var filter express.CompanyFilter
	var pagination express.Pagination

	// 分页参数
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			pagination.Page = page
		} else {
			pagination.Page = 1
		}
	} else {
		pagination.Page = 1
	}

	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil && pageSize > 0 && pageSize <= 100 {
			pagination.PageSize = pageSize
		} else {
			pagination.PageSize = 20
		}
	} else {
		pagination.PageSize = 20
	}

	// 过滤参数
	filter.Keyword = c.Query("keyword")

	if isActiveStr := c.Query("is_active"); isActiveStr != "" {
		if isActive, err := strconv.ParseBool(isActiveStr); err == nil {
			filter.IsActive = &isActive
		}
	}

	filter.SortBy = c.Query("sort_by")
	filter.SortOrder = c.Query("sort_order")

	// 调用服务层
	result, err := h.service.GetCompanies(c.Request.Context(), filter, pagination)
	if err != nil {
		h.logger.Error("Failed to get express companies",
			zap.String("request_id", requestID),
			zap.Any("filter", filter),
			zap.Any("pagination", pagination),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    result,
	})
}

// UpdateCompany 更新快递公司
// @Summary 更新快递公司
// @Description 管理员更新快递公司信息
// @Tags 快递公司管理
// @Accept json
// @Produce json
// @Param id path string true "快递公司ID"
// @Param request body express.UpdateCompanyRequest true "更新快递公司请求"
// @Success 200 {object} express.ExpressCompany "更新成功"
// @Failure 400 {object} errors.ErrorResponse "请求参数错误"
// @Failure 401 {object} errors.ErrorResponse "未授权"
// @Failure 403 {object} errors.ErrorResponse "权限不足"
// @Failure 404 {object} errors.ErrorResponse "快递公司不存在"
// @Failure 500 {object} errors.ErrorResponse "服务器内部错误"
// @Router /api/v1/admin/express/companies/{id} [put]
// @Security BearerAuth
func (h *ExpressCompanyHandler) UpdateCompany(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		h.logger.Warn("Admin user ID not found in context", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	operatorIDStr := operatorID.(string)

	// 获取路径参数
	companyID := c.Param("id")
	if companyID == "" {
		h.logger.Warn("Company ID is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "快递公司ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 解析请求参数
	var req express.UpdateCompanyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request parameters",
			zap.String("request_id", requestID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效: "+err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 调用服务层
	company, err := h.service.UpdateCompany(c.Request.Context(), companyID, req, operatorIDStr)
	if err != nil {
		h.logger.Error("Failed to update express company",
			zap.String("request_id", requestID),
			zap.String("company_id", companyID),
			zap.String("operator_id", operatorIDStr),
			zap.Any("request", req),
			zap.Error(err))

		if err.Error() == "快递公司不存在" {
			businessErr := errors.NewBusinessError(errors.ErrCodeNotFound, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	h.logger.Info("Express company updated successfully",
		zap.String("request_id", requestID),
		zap.String("company_id", companyID),
		zap.String("operator_id", operatorIDStr))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "快递公司更新成功",
		"data":    company,
	})
}

// DeleteCompany 删除快递公司
// @Summary 删除快递公司
// @Description 管理员删除快递公司
// @Tags 快递公司管理
// @Accept json
// @Produce json
// @Param id path string true "快递公司ID"
// @Success 200 {object} map[string]interface{} "删除成功"
// @Failure 400 {object} errors.ErrorResponse "请求参数错误"
// @Failure 401 {object} errors.ErrorResponse "未授权"
// @Failure 403 {object} errors.ErrorResponse "权限不足"
// @Failure 404 {object} errors.ErrorResponse "快递公司不存在"
// @Failure 409 {object} errors.ErrorResponse "存在关联数据，无法删除"
// @Failure 500 {object} errors.ErrorResponse "服务器内部错误"
// @Router /api/v1/admin/express/companies/{id} [delete]
// @Security BearerAuth
func (h *ExpressCompanyHandler) DeleteCompany(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		h.logger.Warn("Admin user ID not found in context", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	operatorIDStr := operatorID.(string)

	// 获取路径参数
	companyID := c.Param("id")
	if companyID == "" {
		h.logger.Warn("Company ID is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "快递公司ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 调用服务层
	err := h.service.DeleteCompany(c.Request.Context(), companyID, operatorIDStr)
	if err != nil {
		h.logger.Error("Failed to delete express company",
			zap.String("request_id", requestID),
			zap.String("company_id", companyID),
			zap.String("operator_id", operatorIDStr),
			zap.Error(err))

		if err.Error() == "快递公司不存在" {
			businessErr := errors.NewBusinessError(errors.ErrCodeNotFound, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else if strings.Contains(err.Error(), "关联") {
			businessErr := errors.NewBusinessError(errors.ErrCodeConflict, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	h.logger.Info("Express company deleted successfully",
		zap.String("request_id", requestID),
		zap.String("company_id", companyID),
		zap.String("operator_id", operatorIDStr))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "快递公司删除成功",
	})
}

// CreateProvider 创建供应商
func (h *ExpressCompanyHandler) CreateProvider(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		h.logger.Warn("Admin user ID not found in context", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	operatorIDStr := operatorID.(string)

	// 解析请求参数
	var req express.CreateProviderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request parameters",
			zap.String("request_id", requestID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效: "+err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 调用服务层
	provider, err := h.service.CreateProvider(c.Request.Context(), req, operatorIDStr)
	if err != nil {
		h.logger.Error("Failed to create express provider",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorIDStr),
			zap.Any("request", req),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	h.logger.Info("Express provider created successfully",
		zap.String("request_id", requestID),
		zap.String("provider_id", provider.ID),
		zap.String("code", provider.Code),
		zap.String("operator_id", operatorIDStr))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "供应商创建成功",
		"data":    provider,
	})
}

// GetProvider 获取供应商详情
func (h *ExpressCompanyHandler) GetProvider(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取路径参数
	providerID := c.Param("id")
	if providerID == "" {
		h.logger.Warn("Provider ID is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "供应商ID不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 调用服务层
	provider, err := h.service.GetProviderByID(c.Request.Context(), providerID)
	if err != nil {
		h.logger.Error("Failed to get express provider",
			zap.String("request_id", requestID),
			zap.String("provider_id", providerID),
			zap.Error(err))

		if err.Error() == "供应商不存在" {
			businessErr := errors.NewBusinessError(errors.ErrCodeNotFound, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    provider,
	})
}

// GetProviders 获取供应商列表
func (h *ExpressCompanyHandler) GetProviders(c *gin.Context) {
	requestID := getRequestID(c)

	// 解析查询参数
	var filter express.ProviderFilter
	var pagination express.Pagination

	// 分页参数
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			pagination.Page = page
		} else {
			pagination.Page = 1
		}
	} else {
		pagination.Page = 1
	}

	if pageSizeStr := c.Query("page_size"); pageSizeStr != "" {
		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil && pageSize > 0 && pageSize <= 100 {
			pagination.PageSize = pageSize
		} else {
			pagination.PageSize = 20
		}
	} else {
		pagination.PageSize = 20
	}

	// 过滤参数
	filter.Keyword = c.Query("keyword")

	if isActiveStr := c.Query("is_active"); isActiveStr != "" {
		if isActive, err := strconv.ParseBool(isActiveStr); err == nil {
			filter.IsActive = &isActive
		}
	}

	filter.SortBy = c.Query("sort_by")
	filter.SortOrder = c.Query("sort_order")

	// 调用服务层
	result, err := h.service.GetProviders(c.Request.Context(), filter, pagination)
	if err != nil {
		h.logger.Error("Failed to get express providers",
			zap.String("request_id", requestID),
			zap.Any("filter", filter),
			zap.Any("pagination", pagination),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "获取成功",
		"data":    result,
	})
}

// ==================== 🚀 新增：状态管理API端点 ====================

// UpdateCompanyStatus 更新快递公司状态
// @Summary 更新快递公司状态
// @Description 管理员更新快递公司启用/禁用状态，支持实时生效
// @Tags 快递公司管理
// @Accept json
// @Produce json
// @Param company_code path string true "快递公司代码"
// @Param request body StatusUpdateRequest true "状态更新请求"
// @Success 200 {object} map[string]interface{} "更新成功"
// @Failure 400 {object} errors.ErrorResponse "请求参数错误"
// @Failure 401 {object} errors.ErrorResponse "未授权"
// @Failure 403 {object} errors.ErrorResponse "权限不足"
// @Failure 404 {object} errors.ErrorResponse "快递公司不存在"
// @Failure 500 {object} errors.ErrorResponse "服务器内部错误"
// @Router /api/v1/admin/express/companies/{company_code}/status [patch]
// @Security BearerAuth
func (h *ExpressCompanyHandler) UpdateCompanyStatus(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		h.logger.Warn("Admin user ID not found in context", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	operatorIDStr := operatorID.(string)

	// 获取路径参数
	companyCode := c.Param("company_code")
	if companyCode == "" {
		h.logger.Warn("Company code is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "快递公司代码不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 解析请求参数
	var req StatusUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request parameters",
			zap.String("request_id", requestID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效: "+err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 调用服务层
	enabled := *req.Enabled // 解引用指针
	err := h.service.UpdateCompanyStatus(c.Request.Context(), companyCode, enabled, operatorIDStr)
	if err != nil {
		h.logger.Error("Failed to update company status",
			zap.String("request_id", requestID),
			zap.String("company_code", companyCode),
			zap.Bool("enabled", enabled),
			zap.String("operator_id", operatorIDStr),
			zap.Error(err))

		if strings.Contains(err.Error(), "不存在") {
			businessErr := errors.NewBusinessError(errors.ErrCodeNotFound, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	h.logger.Info("Company status updated successfully",
		zap.String("request_id", requestID),
		zap.String("company_code", companyCode),
		zap.Bool("enabled", enabled),
		zap.String("operator_id", operatorIDStr))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": fmt.Sprintf("快递公司 %s 状态更新成功", companyCode),
		"data": gin.H{
			"company_code": companyCode,
			"enabled":      enabled,
			"updated_by":   operatorIDStr,
			"updated_at":   time.Now().Format("2006-01-02 15:04:05"),
		},
	})
}

// UpdateProviderStatus 更新供应商状态
// @Summary 更新供应商状态
// @Description 管理员更新供应商启用/禁用状态，支持实时生效
// @Tags 供应商管理
// @Accept json
// @Produce json
// @Param provider_code path string true "供应商代码"
// @Param request body StatusUpdateRequest true "状态更新请求"
// @Success 200 {object} map[string]interface{} "更新成功"
// @Failure 400 {object} errors.ErrorResponse "请求参数错误"
// @Failure 401 {object} errors.ErrorResponse "未授权"
// @Failure 403 {object} errors.ErrorResponse "权限不足"
// @Failure 404 {object} errors.ErrorResponse "供应商不存在"
// @Failure 500 {object} errors.ErrorResponse "服务器内部错误"
// @Router /api/v1/admin/express/providers/{provider_code}/status [patch]
// @Security BearerAuth
func (h *ExpressCompanyHandler) UpdateProviderStatus(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		h.logger.Warn("Admin user ID not found in context", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	operatorIDStr := operatorID.(string)

	// 获取路径参数
	providerCode := c.Param("provider_code")
	if providerCode == "" {
		h.logger.Warn("Provider code is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "供应商代码不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 解析请求参数
	var req StatusUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request parameters",
			zap.String("request_id", requestID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效: "+err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 调用服务层
	enabled := *req.Enabled // 解引用指针
	err := h.service.UpdateProviderStatus(c.Request.Context(), providerCode, enabled, operatorIDStr)
	if err != nil {
		h.logger.Error("Failed to update provider status",
			zap.String("request_id", requestID),
			zap.String("provider_code", providerCode),
			zap.Bool("enabled", enabled),
			zap.String("operator_id", operatorIDStr),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	h.logger.Info("Provider status updated successfully",
		zap.String("request_id", requestID),
		zap.String("provider_code", providerCode),
		zap.Bool("enabled", enabled),
		zap.String("operator_id", operatorIDStr))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": fmt.Sprintf("供应商 %s 状态更新成功", providerCode),
		"data": gin.H{
			"provider_code": providerCode,
			"enabled":       enabled,
			"updated_by":    operatorIDStr,
			"updated_at":    time.Now().Format("2006-01-02 15:04:05"),
		},
	})
}

// UpdateMappingStatus 更新映射关系状态
// @Summary 更新映射关系状态
// @Description 管理员更新快递公司与供应商映射关系的启用/禁用状态，支持实时生效
// @Tags 映射关系管理
// @Accept json
// @Produce json
// @Param company_code path string true "快递公司代码"
// @Param provider_code path string true "供应商代码"
// @Param request body StatusUpdateRequest true "状态更新请求"
// @Success 200 {object} map[string]interface{} "更新成功"
// @Failure 400 {object} errors.ErrorResponse "请求参数错误"
// @Failure 401 {object} errors.ErrorResponse "未授权"
// @Failure 403 {object} errors.ErrorResponse "权限不足"
// @Failure 404 {object} errors.ErrorResponse "映射关系不存在"
// @Failure 500 {object} errors.ErrorResponse "服务器内部错误"
// @Router /api/v1/admin/express/mappings/{company_code}/{provider_code}/status [patch]
// @Security BearerAuth
func (h *ExpressCompanyHandler) UpdateMappingStatus(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		h.logger.Warn("Admin user ID not found in context", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	operatorIDStr := operatorID.(string)

	// 获取路径参数
	companyCode := c.Param("company_code")
	providerCode := c.Param("provider_code")

	if companyCode == "" {
		h.logger.Warn("Company code is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "快递公司代码不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	if providerCode == "" {
		h.logger.Warn("Provider code is required", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "供应商代码不能为空")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	// 解析请求参数
	var req StatusUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.Warn("Invalid request parameters",
			zap.String("request_id", requestID),
			zap.Error(err))
		businessErr := errors.NewBusinessError(errors.ErrCodeInvalidRequest, "请求参数无效: "+err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	// 调用服务层
	enabled := *req.Enabled // 解引用指针
	err := h.service.UpdateMappingStatus(c.Request.Context(), companyCode, providerCode, enabled, operatorIDStr)
	if err != nil {
		h.logger.Error("Failed to update mapping status",
			zap.String("request_id", requestID),
			zap.String("company_code", companyCode),
			zap.String("provider_code", providerCode),
			zap.Bool("enabled", enabled),
			zap.String("operator_id", operatorIDStr),
			zap.Error(err))

		if strings.Contains(err.Error(), "不存在") || strings.Contains(err.Error(), "未找到") {
			businessErr := errors.NewBusinessError(errors.ErrCodeNotFound, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		} else {
			businessErr := errors.NewBusinessError(errors.ErrCodeInternal, err.Error())
			c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		}
		return
	}

	h.logger.Info("Mapping status updated successfully",
		zap.String("request_id", requestID),
		zap.String("company_code", companyCode),
		zap.String("provider_code", providerCode),
		zap.Bool("enabled", enabled),
		zap.String("operator_id", operatorIDStr))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": fmt.Sprintf("映射关系 %s -> %s 状态更新成功", companyCode, providerCode),
		"data": gin.H{
			"company_code":  companyCode,
			"provider_code": providerCode,
			"enabled":       enabled,
			"updated_by":    operatorIDStr,
			"updated_at":    time.Now().Format("2006-01-02 15:04:05"),
		},
	})
}

// executeBatchUpdate 执行批量更新
func (h *ExpressCompanyHandler) executeBatchUpdate(ctx context.Context, req BatchStatusUpdateRequest, operatorID, requestID string) *BatchUpdateResult {
	result := &BatchUpdateResult{
		Results: make([]BatchUpdateItemResult, 0),
	}

	// 更新快递公司状态
	for _, companyCode := range req.Companies {
		itemResult := BatchUpdateItemResult{
			Type: "company",
			Code: companyCode,
		}

		err := h.service.UpdateCompanyStatus(ctx, companyCode, req.Enabled, operatorID)
		if err != nil {
			itemResult.Success = false
			itemResult.Error = err.Error()
			result.FailedCount++
			h.logger.Warn("Failed to update company status in batch",
				zap.String("request_id", requestID),
				zap.String("company_code", companyCode),
				zap.Error(err))
		} else {
			itemResult.Success = true
			result.SuccessCount++
		}

		result.Results = append(result.Results, itemResult)
	}

	// 更新供应商状态
	for _, providerCode := range req.Providers {
		itemResult := BatchUpdateItemResult{
			Type: "provider",
			Code: providerCode,
		}

		err := h.service.UpdateProviderStatus(ctx, providerCode, req.Enabled, operatorID)
		if err != nil {
			itemResult.Success = false
			itemResult.Error = err.Error()
			result.FailedCount++
			h.logger.Warn("Failed to update provider status in batch",
				zap.String("request_id", requestID),
				zap.String("provider_code", providerCode),
				zap.Error(err))
		} else {
			itemResult.Success = true
			result.SuccessCount++
		}

		result.Results = append(result.Results, itemResult)
	}

	// 更新映射关系状态
	for _, mapping := range req.Mappings {
		itemResult := BatchUpdateItemResult{
			Type:         "mapping",
			Code:         fmt.Sprintf("%s->%s", mapping.CompanyCode, mapping.ProviderCode),
			CompanyCode:  mapping.CompanyCode,
			ProviderCode: mapping.ProviderCode,
		}

		err := h.service.UpdateMappingStatus(ctx, mapping.CompanyCode, mapping.ProviderCode, req.Enabled, operatorID)
		if err != nil {
			itemResult.Success = false
			itemResult.Error = err.Error()
			result.FailedCount++
			h.logger.Warn("Failed to update mapping status in batch",
				zap.String("request_id", requestID),
				zap.String("company_code", mapping.CompanyCode),
				zap.String("provider_code", mapping.ProviderCode),
				zap.Error(err))
		} else {
			itemResult.Success = true
			result.SuccessCount++
		}

		result.Results = append(result.Results, itemResult)
	}

	return result
}

// RefreshCache 刷新缓存
// @Summary 刷新缓存
// @Description 管理员手动刷新快递公司和映射关系缓存
// @Tags 缓存管理
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{} "刷新成功"
// @Failure 401 {object} errors.ErrorResponse "未授权"
// @Failure 403 {object} errors.ErrorResponse "权限不足"
// @Failure 500 {object} errors.ErrorResponse "服务器内部错误"
// @Router /api/v1/admin/express/cache/refresh [post]
// @Security BearerAuth
func (h *ExpressCompanyHandler) RefreshCache(c *gin.Context) {
	requestID := getRequestID(c)

	// 获取操作者ID
	operatorID, exists := c.Get("admin_user_id")
	if !exists {
		h.logger.Warn("Admin user ID not found in context", zap.String("request_id", requestID))
		err := errors.NewBusinessError(errors.ErrCodeUnauthorized, "用户未认证")
		c.JSON(err.HTTPStatus(), errors.ToErrorResponse(err, requestID))
		return
	}

	operatorIDStr := operatorID.(string)

	// 刷新缓存
	startTime := time.Now()
	err := h.service.RefreshCache(c.Request.Context())
	duration := time.Since(startTime)

	if err != nil {
		h.logger.Error("Failed to refresh cache",
			zap.String("request_id", requestID),
			zap.String("operator_id", operatorIDStr),
			zap.Duration("duration", duration),
			zap.Error(err))

		businessErr := errors.NewBusinessError(errors.ErrCodeInternal, "缓存刷新失败: "+err.Error())
		c.JSON(businessErr.HTTPStatus(), errors.ToErrorResponse(businessErr, requestID))
		return
	}

	h.logger.Info("Cache refreshed successfully",
		zap.String("request_id", requestID),
		zap.String("operator_id", operatorIDStr),
		zap.Duration("duration", duration))

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"code":    200,
		"message": "缓存刷新成功",
		"data": gin.H{
			"refreshed_at": time.Now().Format("2006-01-02 15:04:05"),
			"duration_ms":  duration.Milliseconds(),
			"operator_id":  operatorIDStr,
		},
	})
}
